<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <g transform="translate(960, 120)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
      第六剑：一直卖
    </text>
  </g>
  
  <!-- 主视觉：声音图标发出声波 -->
  <g transform="translate(960, 400)">
    <!-- 声音图标 -->
    <g transform="translate(0, 0)">
      <!-- 扬声器主体 -->
      <rect x="-40" y="-30" width="50" height="60" fill="#e67e22" rx="5"/>
      <rect x="-35" y="-25" width="40" height="50" fill="#d35400" rx="3"/>
      
      <!-- 扬声器喇叭口 -->
      <path d="M 10 -30 L 60 -60 L 60 60 L 10 30 Z" fill="#e67e22"/>
      <path d="M 15 -25 L 55 -50 L 55 50 L 15 25 Z" fill="#d35400"/>
      
      <!-- 扬声器网格 -->
      <g opacity="0.6">
        <line x1="-25" y1="-15" x2="-15" y2="-15" stroke="#ffffff" stroke-width="2"/>
        <line x1="-25" y1="-5" x2="-15" y2="-5" stroke="#ffffff" stroke-width="2"/>
        <line x1="-25" y1="5" x2="-15" y2="5" stroke="#ffffff" stroke-width="2"/>
        <line x1="-25" y1="15" x2="-15" y2="15" stroke="#ffffff" stroke-width="2"/>
      </g>
    </g>
    
    <!-- 声波1 -->
    <g transform="translate(150, 0)" opacity="0.8">
      <path d="M 0 -80 Q 40 -40 0 0 Q 40 40 0 80" stroke="#f39c12" stroke-width="6" fill="none"/>
      
      <!-- 钉子关键词 -->
      <rect x="-30" y="-15" width="60" height="30" fill="#f39c12" rx="15"/>
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="14" font-weight="bold" fill="#ffffff">
        钉子
      </text>
    </g>
    
    <!-- 声波2 -->
    <g transform="translate(250, 0)" opacity="0.6">
      <path d="M 0 -120 Q 60 -60 0 0 Q 60 60 0 120" stroke="#f1c40f" stroke-width="5" fill="none"/>
      
      <!-- 钉子关键词 -->
      <rect x="-30" y="-15" width="60" height="30" fill="#f1c40f" rx="15"/>
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="14" font-weight="bold" fill="#ffffff">
        钉子
      </text>
    </g>
    
    <!-- 声波3 -->
    <g transform="translate(350, 0)" opacity="0.4">
      <path d="M 0 -160 Q 80 -80 0 0 Q 80 80 0 160" stroke="#f39c12" stroke-width="4" fill="none"/>
      
      <!-- 钉子关键词 -->
      <rect x="-30" y="-15" width="60" height="30" fill="#f39c12" rx="15"/>
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="14" font-weight="bold" fill="#ffffff">
        钉子
      </text>
    </g>
    
    <!-- 声波4 -->
    <g transform="translate(450, 0)" opacity="0.2">
      <path d="M 0 -200 Q 100 -100 0 0 Q 100 100 0 200" stroke="#f1c40f" stroke-width="3" fill="none"/>
      
      <!-- 钉子关键词 -->
      <rect x="-30" y="-15" width="60" height="30" fill="#f1c40f" rx="15"/>
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="14" font-weight="bold" fill="#ffffff">
        钉子
      </text>
    </g>
    
    <!-- 重复强调的文字 -->
    <g transform="translate(-200, -100)" opacity="0.7">
      <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" font-weight="bold" fill="#e67e22">
        反复提及
      </text>
    </g>
    
    <g transform="translate(-200, 0)" opacity="0.7">
      <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" font-weight="bold" fill="#e67e22">
        反复强调
      </text>
    </g>
    
    <g transform="translate(-200, 100)" opacity="0.7">
      <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" font-weight="bold" fill="#e67e22">
        反复应用
      </text>
    </g>
  </g>
  
  <!-- 核心理念 -->
  <g transform="translate(960, 650)">
    <rect x="-500" y="-30" width="1000" height="60" fill="#fef5e7" rx="30"/>
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" font-weight="bold" fill="#e67e22">
      在课程的每一个环节，都不断地重复和强化你的"钉子"。
    </text>
  </g>
  
  <!-- 目标说明 -->
  <g transform="translate(960, 750)">
    <rect x="-400" y="-30" width="800" height="60" fill="#e67e22" rx="30"/>
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" font-weight="bold" fill="#ffffff">
      让你的"钉子"，成为学员脑海中的常识。
    </text>
  </g>
  
  <!-- 装饰性重复元素 -->
  <g opacity="0.2">
    <!-- 左侧循环箭头 -->
    <g transform="translate(200, 500)">
      <circle cx="0" cy="0" r="25" fill="none" stroke="#e67e22" stroke-width="4"/>
      
      <!-- 箭头 -->
      <defs>
        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
          <polygon points="0 0, 10 3.5, 0 7" fill="#e67e22"/>
        </marker>
      </defs>
      
      <path d="M 0 -25 Q 25 -25 25 0 Q 25 25 0 25 Q -25 25 -25 0 Q -25 -25 0 -25" 
            stroke="#e67e22" stroke-width="4" fill="none" marker-end="url(#arrowhead)"/>
    </g>
    
    <!-- 右侧重复符号 -->
    <g transform="translate(1720, 450)">
      <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="48" font-weight="bold" fill="#e67e22">
        ∞
      </text>
      <text x="0" y="40" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#e67e22">
        无限循环
      </text>
    </g>
  </g>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.3">
    <path d="M 100 950 Q 960 900 1820 950" stroke="#e67e22" stroke-width="4" fill="none"/>
  </g>
</svg>
