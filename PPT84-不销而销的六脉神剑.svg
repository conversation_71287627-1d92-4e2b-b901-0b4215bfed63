<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <g transform="translate(960, 150)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
      "不销而销"的六脉神剑
    </text>
  </g>
  
  <!-- 六边形雷达图 -->
  <g transform="translate(960, 500)">
    <!-- 六边形外框 -->
    <g opacity="0.3">
      <polygon points="0,-200 173,-100 173,100 0,200 -173,100 -173,-100" 
               fill="none" stroke="#bdc3c7" stroke-width="2"/>
      <polygon points="0,-150 130,-75 130,75 0,150 -130,75 -130,-75" 
               fill="none" stroke="#bdc3c7" stroke-width="2"/>
      <polygon points="0,-100 87,-50 87,50 0,100 -87,50 -87,-50" 
               fill="none" stroke="#bdc3c7" stroke-width="2"/>
      <polygon points="0,-50 43,-25 43,25 0,50 -43,25 -43,-25" 
               fill="none" stroke="#bdc3c7" stroke-width="2"/>
    </g>
    
    <!-- 六个顶点的武器 -->
    <!-- 1. 卖话术 (顶部) -->
    <g transform="translate(0, -200)">
      <circle cx="0" cy="0" r="40" fill="#e74c3c"/>
      <circle cx="0" cy="0" r="30" fill="#c0392b"/>
      
      <!-- 文案图标 -->
      <rect x="-15" y="-10" width="30" height="20" fill="#ffffff" rx="3"/>
      <line x1="-10" y1="-5" x2="10" y2="-5" stroke="#e74c3c" stroke-width="2"/>
      <line x1="-10" y1="0" x2="10" y2="0" stroke="#e74c3c" stroke-width="2"/>
      <line x1="-10" y1="5" x2="5" y2="5" stroke="#e74c3c" stroke-width="2"/>
      
      <text x="0" y="65" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" font-weight="bold" fill="#e74c3c">
        卖话术
      </text>
      <text x="0" y="85" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#7f8c8d">
        (课程内容即文案)
      </text>
    </g>
    
    <!-- 2. 卖信任 (右上) -->
    <g transform="translate(173, -100)">
      <circle cx="0" cy="0" r="40" fill="#27ae60"/>
      <circle cx="0" cy="0" r="30" fill="#2ecc71"/>
      
      <!-- 信任图标 -->
      <path d="M 0 15 Q -15 0 -30 10 Q -30 25 0 40 Q 30 25 30 10 Q 15 0 0 15 Z" fill="#ffffff"/>
      <path d="M -8 5 L -3 10 L 8 -2" stroke="#27ae60" stroke-width="3" fill="none"/>
      
      <text x="0" y="65" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" font-weight="bold" fill="#27ae60">
        卖信任
      </text>
      <text x="0" y="85" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#7f8c8d">
        (客户故事即证明)
      </text>
    </g>
    
    <!-- 3. 卖一个 (右下) -->
    <g transform="translate(173, 100)">
      <circle cx="0" cy="0" r="40" fill="#3498db"/>
      <circle cx="0" cy="0" r="30" fill="#2980b9"/>
      
      <!-- 数字1图标 -->
      <text x="0" y="8" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="32" font-weight="bold" fill="#ffffff">
        1
      </text>
      
      <text x="0" y="65" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" font-weight="bold" fill="#3498db">
        卖一个
      </text>
      <text x="0" y="85" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#7f8c8d">
        (单一产品降门槛)
      </text>
    </g>
    
    <!-- 4. 卖一致 (底部) -->
    <g transform="translate(0, 200)">
      <circle cx="0" cy="0" r="40" fill="#f39c12"/>
      <circle cx="0" cy="0" r="30" fill="#e67e22"/>
      
      <!-- 一致性图标 -->
      <rect x="-15" y="-8" width="30" height="4" fill="#ffffff" rx="2"/>
      <rect x="-15" y="-2" width="30" height="4" fill="#ffffff" rx="2"/>
      <rect x="-15" y="4" width="30" height="4" fill="#ffffff" rx="2"/>
      
      <text x="0" y="65" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" font-weight="bold" fill="#f39c12">
        卖一致
      </text>
      <text x="0" y="85" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#7f8c8d">
        (讲A卖A不跑偏)
      </text>
    </g>
    
    <!-- 5. 提前卖 (左下) -->
    <g transform="translate(-173, 100)">
      <circle cx="0" cy="0" r="40" fill="#9b59b6"/>
      <circle cx="0" cy="0" r="30" fill="#8e44ad"/>
      
      <!-- 时钟图标 -->
      <circle cx="0" cy="0" r="12" fill="#ffffff"/>
      <line x1="0" y1="0" x2="0" y2="-8" stroke="#9b59b6" stroke-width="2"/>
      <line x1="0" y1="0" x2="6" y2="0" stroke="#9b59b6" stroke-width="2"/>
      <circle cx="0" cy="0" r="2" fill="#9b59b6"/>
      
      <text x="0" y="65" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" font-weight="bold" fill="#9b59b6">
        提前卖
      </text>
      <text x="0" y="85" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#7f8c8d">
        (价值前置建渴望)
      </text>
    </g>
    
    <!-- 6. 一直卖 (左上) -->
    <g transform="translate(-173, -100)">
      <circle cx="0" cy="0" r="40" fill="#e67e22"/>
      <circle cx="0" cy="0" r="30" fill="#d35400"/>
      
      <!-- 循环图标 -->
      <path d="M -10 -5 Q -15 -15 0 -15 Q 15 -15 10 -5" stroke="#ffffff" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
      <path d="M 10 5 Q 15 15 0 15 Q -15 15 -10 5" stroke="#ffffff" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
      
      <text x="0" y="65" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" font-weight="bold" fill="#e67e22">
        一直卖
      </text>
      <text x="0" y="85" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#7f8c8d">
        (核心钉子反复锤)
      </text>
    </g>
    
    <!-- 中心连接线 -->
    <g opacity="0.4">
      <line x1="0" y1="0" x2="0" y2="-200" stroke="#e74c3c" stroke-width="2"/>
      <line x1="0" y1="0" x2="173" y2="-100" stroke="#27ae60" stroke-width="2"/>
      <line x1="0" y1="0" x2="173" y2="100" stroke="#3498db" stroke-width="2"/>
      <line x1="0" y1="0" x2="0" y2="200" stroke="#f39c12" stroke-width="2"/>
      <line x1="0" y1="0" x2="-173" y2="100" stroke="#9b59b6" stroke-width="2"/>
      <line x1="0" y1="0" x2="-173" y2="-100" stroke="#e67e22" stroke-width="2"/>
    </g>
    
    <!-- 中心圆 -->
    <circle cx="0" cy="0" r="20" fill="#2c3e50"/>
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="12" font-weight="bold" fill="#ffffff">
      六剑
    </text>
  </g>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="arrowhead" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
      <polygon points="0 0, 8 3, 0 6" fill="#ffffff"/>
    </marker>
  </defs>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.3">
    <path d="M 100 950 Q 960 900 1820 950" stroke="#2c3e50" stroke-width="4" fill="none"/>
  </g>
</svg>
