<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <g transform="translate(960, 120)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
      标准一：一目了然 (视觉清晰)
    </text>
  </g>
  
  <!-- 计时器图标 -->
  <g transform="translate(960, 250)">
    <circle cx="0" cy="0" r="60" fill="#e74c3c" opacity="0.2"/>
    <circle cx="0" cy="0" r="45" fill="#c0392b"/>
    <circle cx="0" cy="0" r="35" fill="#e74c3c"/>
    
    <!-- 时钟指针 -->
    <line x1="0" y1="0" x2="0" y2="-25" stroke="#ffffff" stroke-width="4"/>
    <line x1="0" y1="0" x2="15" y2="0" stroke="#ffffff" stroke-width="3"/>
    <circle cx="0" cy="0" r="4" fill="#ffffff"/>
    
    <!-- 3秒标识 -->
    <text x="0" y="90" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="48" font-weight="bold" fill="#e74c3c">
      3秒
    </text>
  </g>
  
  <!-- 对比区域 -->
  <!-- 左侧：错误示例 -->
  <g transform="translate(480, 600)">
    <!-- 背景区域 -->
    <rect x="-350" y="-200" width="700" height="400" fill="#ffebee" rx="20"/>
    <rect x="-330" y="-180" width="660" height="360" fill="#ffcdd2" opacity="0.5" rx="15"/>
    
    <!-- 红叉标记 -->
    <g transform="translate(-250, -150)">
      <circle cx="0" cy="0" r="20" fill="#e74c3c"/>
      <path d="M -10 -10 L 10 10 M 10 -10 L -10 10" stroke="#ffffff" stroke-width="3"/>
    </g>
    
    <!-- 文字堆砌示例 -->
    <g transform="translate(0, -100)">
      <rect x="-200" y="-60" width="400" height="120" fill="#ffffff" rx="10"/>
      <rect x="-190" y="-50" width="380" height="100" fill="#f5f5f5" rx="8"/>
      
      <!-- 密密麻麻的文字线条 -->
      <line x1="-170" y1="-30" x2="170" y2="-30" stroke="#7f8c8d" stroke-width="2"/>
      <line x1="-170" y1="-15" x2="170" y2="-15" stroke="#7f8c8d" stroke-width="2"/>
      <line x1="-170" y1="0" x2="170" y2="0" stroke="#7f8c8d" stroke-width="2"/>
      <line x1="-170" y1="15" x2="170" y2="15" stroke="#7f8c8d" stroke-width="2"/>
      <line x1="-170" y1="30" x2="170" y2="30" stroke="#7f8c8d" stroke-width="2"/>
      <line x1="-170" y1="45" x2="120" y2="45" stroke="#7f8c8d" stroke-width="2"/>
    </g>
    
    <text x="0" y="50" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" font-weight="bold" fill="#e74c3c">
      文字堆砌
    </text>
    <text x="0" y="80" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="22" fill="#7f8c8d">
      像一份政府工作报告。
    </text>
    
    <text x="0" y="120" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="20" font-weight="bold" fill="#e74c3c">
      挑战用户：
    </text>
    <text x="0" y="145" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" fill="#7f8c8d">
      让用户去"研究"和"理解"。
    </text>
  </g>
  
  <!-- 右侧：正确示例 -->
  <g transform="translate(1440, 600)">
    <!-- 背景区域 -->
    <rect x="-350" y="-200" width="700" height="400" fill="#e8f5e8" rx="20"/>
    <rect x="-330" y="-180" width="660" height="360" fill="#c8e6c9" opacity="0.5" rx="15"/>
    
    <!-- 绿勾标记 -->
    <g transform="translate(-250, -150)">
      <circle cx="0" cy="0" r="20" fill="#27ae60"/>
      <path d="M -8 0 L -3 8 L 10 -8" stroke="#ffffff" stroke-width="3" fill="none"/>
    </g>
    
    <!-- 图形化示例 -->
    <g transform="translate(0, -100)">
      <rect x="-150" y="-60" width="300" height="120" fill="#ffffff" rx="10"/>
      <rect x="-140" y="-50" width="280" height="100" fill="#f8f9fa" rx="8"/>
      
      <!-- 简洁的图形元素 -->
      <circle cx="-80" cy="-20" r="15" fill="#3498db"/>
      <path d="M -50 -20 L 50 -20" stroke="#27ae60" stroke-width="4" marker-end="url(#arrowhead)"/>
      <circle cx="80" cy="-20" r="15" fill="#e74c3c"/>
      
      <!-- 简洁文字 -->
      <text x="-80" y="20" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#2c3e50">
        起点
      </text>
      <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="12" fill="#27ae60">
        路径
      </text>
      <text x="80" y="20" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#2c3e50">
        终点
      </text>
    </g>
    
    <text x="0" y="50" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" font-weight="bold" fill="#27ae60">
      图形化
    </text>
    <text x="0" y="80" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="22" fill="#7f8c8d">
      像一张寻宝图。
    </text>
    
    <text x="0" y="120" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="20" font-weight="bold" fill="#27ae60">
      服务用户：
    </text>
    <text x="0" y="145" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" fill="#7f8c8d">
      让用户能"秒懂"和"感受"。
    </text>
  </g>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#27ae60"/>
    </marker>
  </defs>
  
  <!-- 装饰性眼睛图标 -->
  <g transform="translate(200, 300)" opacity="0.2">
    <ellipse cx="0" cy="0" rx="30" ry="20" fill="#3498db"/>
    <circle cx="0" cy="0" r="12" fill="#ffffff"/>
    <circle cx="0" cy="0" r="8" fill="#2c3e50"/>
  </g>
  
  <g transform="translate(1720, 400)" opacity="0.2">
    <ellipse cx="0" cy="0" rx="30" ry="20" fill="#27ae60"/>
    <circle cx="0" cy="0" r="12" fill="#ffffff"/>
    <circle cx="0" cy="0" r="8" fill="#2c3e50"/>
  </g>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.3">
    <path d="M 100 950 Q 960 900 1820 950" stroke="#3498db" stroke-width="4" fill="none"/>
  </g>
</svg>
