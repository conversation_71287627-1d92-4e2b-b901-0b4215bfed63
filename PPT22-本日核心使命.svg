<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <g transform="translate(960, 150)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
      本日核心使命
    </text>
  </g>
  
  <!-- 左侧：上午任务 -->
  <g transform="translate(480, 540)">
    <!-- 背景区域 -->
    <rect x="-350" y="-300" width="700" height="600" fill="#e8f6f3" rx="30"/>
    <rect x="-330" y="-280" width="660" height="560" fill="#d5f4e6" opacity="0.5" rx="25"/>
    
    <!-- 地图/框架图标 -->
    <g transform="translate(0, -150)">
      <rect x="-80" y="-60" width="160" height="120" fill="#27ae60" rx="15"/>
      <rect x="-70" y="-50" width="140" height="100" fill="#2ecc71" rx="10"/>
      
      <!-- 地图网格 -->
      <g opacity="0.6">
        <line x1="-50" y1="-30" x2="50" y2="-30" stroke="#ffffff" stroke-width="2"/>
        <line x1="-50" y1="0" x2="50" y2="0" stroke="#ffffff" stroke-width="2"/>
        <line x1="-50" y1="30" x2="50" y2="30" stroke="#ffffff" stroke-width="2"/>
        <line x1="-30" y1="-40" x2="-30" y2="40" stroke="#ffffff" stroke-width="2"/>
        <line x1="0" y1="-40" x2="0" y2="40" stroke="#ffffff" stroke-width="2"/>
        <line x1="30" y1="-40" x2="30" y2="40" stroke="#ffffff" stroke-width="2"/>
      </g>
      
      <!-- 路径标记 -->
      <circle cx="-40" cy="-20" r="6" fill="#e74c3c"/>
      <circle cx="0" cy="10" r="6" fill="#f39c12"/>
      <circle cx="40" cy="-10" r="6" fill="#3498db"/>
      
      <!-- 连接线 -->
      <path d="M -40 -20 Q -20 -5 0 10 Q 20 0 40 -10" stroke="#ffffff" stroke-width="3" fill="none"/>
    </g>
    
    <!-- 标题 -->
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="36" font-weight="bold" fill="#27ae60">
      上午：能力落地
    </text>
    
    <!-- 描述 -->
    <text x="0" y="60" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" fill="#2c3e50">
      将你的战略"钉子"，转化为一张
    </text>
    <text x="0" y="90" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" fill="#2c3e50">
      让用户无条件信赖的
    </text>
    <text x="0" y="120" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" font-weight="bold" fill="#27ae60">
      "解决方案地图"（框架）。
    </text>
    
    <!-- 关键词标签 -->
    <g transform="translate(0, 200)">
      <rect x="-60" y="-20" width="120" height="40" fill="#27ae60" rx="20"/>
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" font-weight="bold" fill="#ffffff">
        解决"信"的问题
      </text>
    </g>
  </g>
  
  <!-- 右侧：下午任务 -->
  <g transform="translate(1440, 540)">
    <!-- 背景区域 -->
    <rect x="-350" y="-300" width="700" height="600" fill="#fef5e7" rx="30"/>
    <rect x="-330" y="-280" width="660" height="560" fill="#fdeaa7" opacity="0.5" rx="25"/>
    
    <!-- 火焰图标 -->
    <g transform="translate(0, -150)">
      <ellipse cx="0" cy="40" rx="50" ry="20" fill="#e67e22"/>
      
      <!-- 火焰层次 -->
      <path d="M -40 40 Q -20 0 0 40 Q 20 0 40 40" fill="#e74c3c"/>
      <path d="M -30 30 Q -10 -10 10 30 Q 30 -10 30 30" fill="#f39c12"/>
      <path d="M -20 20 Q 0 -20 20 20" fill="#f1c40f"/>
      <path d="M -10 10 Q 0 -30 10 10" fill="#ffffff" opacity="0.8"/>
      
      <!-- 火花效果 -->
      <g opacity="0.7">
        <circle cx="-50" cy="20" r="3" fill="#f39c12"/>
        <circle cx="50" cy="15" r="2" fill="#e74c3c"/>
        <circle cx="-45" cy="-10" r="2" fill="#f1c40f"/>
        <circle cx="45" cy="-5" r="3" fill="#e67e22"/>
      </g>
    </g>
    
    <!-- 标题 -->
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="36" font-weight="bold" fill="#e67e22">
      下午：技能掌握
    </text>
    
    <!-- 描述 -->
    <text x="0" y="60" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" fill="#2c3e50">
      掌握"大片式开场"的核心秘诀，
    </text>
    <text x="0" y="90" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" fill="#2c3e50">
      撰写一份足以点燃学员、
    </text>
    <text x="0" y="120" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" fill="#2c3e50">
      使其决心改命的
    </text>
    <text x="0" y="150" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" font-weight="bold" fill="#e67e22">
      "黄金开场"逐字稿。
    </text>
    
    <!-- 关键词标签 -->
    <g transform="translate(0, 220)">
      <rect x="-60" y="-20" width="120" height="40" fill="#e67e22" rx="20"/>
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" font-weight="bold" fill="#ffffff">
        解决"燃"的问题
      </text>
    </g>
  </g>
  
  <!-- 中间连接元素 -->
  <g transform="translate(960, 540)">
    <circle cx="0" cy="0" r="40" fill="#3498db"/>
    <text x="0" y="8" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" font-weight="bold" fill="#ffffff">
      +
    </text>
    
    <!-- 连接线 -->
    <path d="M -400 0 L -50 0" stroke="#27ae60" stroke-width="4" fill="none" opacity="0.6"/>
    <path d="M 50 0 L 400 0" stroke="#e67e22" stroke-width="4" fill="none" opacity="0.6"/>
  </g>
  
  <!-- 底部总结 -->
  <g transform="translate(960, 850)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="32" fill="#2c3e50">
      今天过后，你的课程将不再只是一句有力的口号，
    </text>
    <text x="0" y="40" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="32" fill="#2c3e50">
      它将拥有清晰的"骨架"和动人的"灵魂"。
    </text>
  </g>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.3">
    <path d="M 100 950 Q 960 900 1820 950" stroke="#3498db" stroke-width="4" fill="none"/>
  </g>
</svg>
