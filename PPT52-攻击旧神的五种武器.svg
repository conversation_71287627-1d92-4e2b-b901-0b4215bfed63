<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <g transform="translate(960, 120)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
      攻击"旧神"的五种武器
    </text>
  </g>
  
  <!-- 武器1：数据反常 -->
  <g transform="translate(320, 300)">
    <rect x="-150" y="-80" width="300" height="160" fill="#e8f6f3" rx="15"/>
    
    <!-- 数据图表图标 -->
    <g transform="translate(0, -30)">
      <rect x="-40" y="-20" width="80" height="40" fill="#ffffff" rx="5"/>
      <rect x="-35" y="-15" width="70" height="30" fill="#ecf0f1" rx="3"/>
      
      <!-- 图表线条 -->
      <path d="M -30 5 L -10 -5 L 10 10 L 30 -10" stroke="#27ae60" stroke-width="3" fill="none"/>
      <circle cx="-30" cy="5" r="3" fill="#27ae60"/>
      <circle cx="-10" cy="-5" r="3" fill="#27ae60"/>
      <circle cx="10" cy="10" r="3" fill="#27ae60"/>
      <circle cx="30" cy="-10" r="3" fill="#27ae60"/>
    </g>
    
    <text x="0" y="30" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" font-weight="bold" fill="#27ae60">
      数据反常
    </text>
  </g>
  
  <!-- 武器2：逻辑悖论 -->
  <g transform="translate(960, 300)">
    <rect x="-150" y="-80" width="300" height="160" fill="#fef5e7" rx="15"/>
    
    <!-- 互相矛盾的箭头图标 -->
    <g transform="translate(0, -30)">
      <path d="M -40 -10 L 40 -10" stroke="#f39c12" stroke-width="4" marker-end="url(#arrowhead1)"/>
      <path d="M 40 10 L -40 10" stroke="#e67e22" stroke-width="4" marker-end="url(#arrowhead2)"/>
      
      <!-- 冲突标记 -->
      <circle cx="0" cy="0" r="15" fill="#e74c3c" opacity="0.3"/>
      <path d="M -8 -8 L 8 8 M 8 -8 L -8 8" stroke="#e74c3c" stroke-width="3"/>
    </g>
    
    <text x="0" y="30" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" font-weight="bold" fill="#f39c12">
      逻辑悖论
    </text>
  </g>
  
  <!-- 武器3：历史溯源 -->
  <g transform="translate(1600, 300)">
    <rect x="-150" y="-80" width="300" height="160" fill="#ebf3fd" rx="15"/>
    
    <!-- 古老卷轴图标 -->
    <g transform="translate(0, -30)">
      <rect x="-35" y="-20" width="70" height="40" fill="#8e44ad" rx="5"/>
      <rect x="-30" y="-15" width="60" height="30" fill="#9b59b6" rx="3"/>
      
      <!-- 卷轴文字线条 -->
      <line x1="-20" y1="-8" x2="20" y2="-8" stroke="#ffffff" stroke-width="2"/>
      <line x1="-20" y1="-2" x2="20" y2="-2" stroke="#ffffff" stroke-width="2"/>
      <line x1="-20" y1="4" x2="20" y2="4" stroke="#ffffff" stroke-width="2"/>
      <line x1="-20" y1="10" x2="15" y2="10" stroke="#ffffff" stroke-width="2"/>
    </g>
    
    <text x="0" y="30" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" font-weight="bold" fill="#3498db">
      历史溯源
    </text>
  </g>
  
  <!-- 武器4：案例失效 -->
  <g transform="translate(480, 600)">
    <rect x="-150" y="-80" width="300" height="160" fill="#ffebee" rx="15"/>
    
    <!-- 奖杯被打碎图标 -->
    <g transform="translate(0, -30)">
      <!-- 奖杯主体 -->
      <rect x="-15" y="-10" width="30" height="25" fill="#f1c40f" rx="3"/>
      <rect x="-20" y="15" width="40" height="8" fill="#e67e22" rx="2"/>
      <rect x="-10" y="23" width="20" height="12" fill="#d35400" rx="2"/>
      
      <!-- 裂缝 -->
      <path d="M -10 -10 L 10 15" stroke="#e74c3c" stroke-width="3"/>
      <path d="M 0 -5 L 15 20" stroke="#c0392b" stroke-width="2"/>
      
      <!-- 碎片 -->
      <circle cx="-25" cy="0" r="3" fill="#f1c40f"/>
      <circle cx="25" cy="5" r="2" fill="#e67e22"/>
    </g>
    
    <text x="0" y="30" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" font-weight="bold" fill="#e74c3c">
      案例失效
    </text>
  </g>
  
  <!-- 武器5：后果放大 -->
  <g transform="translate(1440, 600)">
    <rect x="-150" y="-80" width="300" height="160" fill="#f8f9fa" rx="15"/>
    
    <!-- 多米诺骨牌图标 -->
    <g transform="translate(0, -30)">
      <!-- 第一块（倒下） -->
      <rect x="-40" y="-5" width="8" height="25" fill="#95a5a6" rx="2" transform="rotate(30 -36 7)"/>
      
      <!-- 第二块（正在倒） -->
      <rect x="-20" y="-10" width="8" height="25" fill="#7f8c8d" rx="2" transform="rotate(15 -16 2)"/>
      
      <!-- 第三块（直立） -->
      <rect x="0" y="-15" width="8" height="25" fill="#95a5a6" rx="2"/>
      
      <!-- 第四块（直立） -->
      <rect x="20" y="-15" width="8" height="25" fill="#7f8c8d" rx="2"/>
      
      <!-- 第五块（直立） -->
      <rect x="40" y="-15" width="8" height="25" fill="#95a5a6" rx="2"/>
      
      <!-- 冲击波 -->
      <path d="M -50 10 Q -30 5 -10 10" stroke="#e74c3c" stroke-width="2" fill="none" opacity="0.6"/>
    </g>
    
    <text x="0" y="30" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" font-weight="bold" fill="#9b59b6">
      后果放大
    </text>
  </g>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="arrowhead1" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#f39c12"/>
    </marker>
    <marker id="arrowhead2" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#e67e22"/>
    </marker>
  </defs>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.3">
    <path d="M 100 950 Q 960 900 1820 950" stroke="#9b59b6" stroke-width="4" fill="none"/>
  </g>
</svg>
