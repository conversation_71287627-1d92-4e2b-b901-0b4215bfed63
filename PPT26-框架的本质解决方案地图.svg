<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <g transform="translate(960, 150)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
      框架的本质：一张"解决方案地图"
    </text>
  </g>
  
  <!-- 中央信任印章 -->
  <g transform="translate(960, 400)">
    <!-- 印章外圈 -->
    <circle cx="0" cy="0" r="120" fill="#e74c3c" opacity="0.2"/>
    <circle cx="0" cy="0" r="100" fill="#c0392b" opacity="0.3"/>
    <circle cx="0" cy="0" r="80" fill="#e74c3c"/>
    
    <!-- 印章内容 -->
    <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="32" font-weight="bold" fill="#ffffff">
      信任
    </text>
    <text x="0" y="20" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="16" fill="#ffffff">
      TRUST
    </text>
    
    <!-- 印章装饰边框 -->
    <circle cx="0" cy="0" r="90" fill="none" stroke="#ffffff" stroke-width="2"/>
    <circle cx="0" cy="0" r="70" fill="none" stroke="#ffffff" stroke-width="1"/>
  </g>
  
  <!-- 左侧：确定感 -->
  <g transform="translate(400, 400)">
    <circle cx="0" cy="0" r="80" fill="#3498db" opacity="0.2"/>
    <circle cx="0" cy="0" r="60" fill="#2980b9" opacity="0.3"/>
    
    <!-- 盾牌图标 -->
    <g transform="translate(0, -10)">
      <path d="M 0 -30 Q -25 -25 -25 0 Q -25 25 0 40 Q 25 25 25 0 Q 25 -25 0 -30 Z" fill="#3498db"/>
      <path d="M 0 -25 Q -20 -20 -20 0 Q -20 20 0 35 Q 20 20 20 0 Q 20 -20 0 -25 Z" fill="#2980b9"/>
      <path d="M -10 -5 L -5 5 L 10 -10" stroke="#ffffff" stroke-width="3" fill="none"/>
    </g>
    
    <text x="0" y="70" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" font-weight="bold" fill="#3498db">
      确定感
    </text>
    <text x="0" y="100" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" fill="#7f8c8d">
      (降低恐惧)
    </text>
  </g>
  
  <!-- 右侧：掌控感 -->
  <g transform="translate(1520, 400)">
    <circle cx="0" cy="0" r="80" fill="#f39c12" opacity="0.2"/>
    <circle cx="0" cy="0" r="60" fill="#e67e22" opacity="0.3"/>
    
    <!-- 方向盘图标 -->
    <g transform="translate(0, -10)">
      <circle cx="0" cy="0" r="25" fill="#f39c12"/>
      <circle cx="0" cy="0" r="20" fill="#e67e22"/>
      <circle cx="0" cy="0" r="8" fill="#d35400"/>
      
      <!-- 方向盘辐条 -->
      <line x1="0" y1="-20" x2="0" y2="-8" stroke="#ffffff" stroke-width="3"/>
      <line x1="0" y1="20" x2="0" y2="8" stroke="#ffffff" stroke-width="3"/>
      <line x1="-20" y1="0" x2="-8" y2="0" stroke="#ffffff" stroke-width="3"/>
      <line x1="20" y1="0" x2="8" y2="0" stroke="#ffffff" stroke-width="3"/>
    </g>
    
    <text x="0" y="70" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" font-weight="bold" fill="#f39c12">
      掌控感
    </text>
    <text x="0" y="100" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" fill="#7f8c8d">
      (给予勇气)
    </text>
  </g>
  
  <!-- 连接线 -->
  <g opacity="0.4">
    <path d="M 480 400 L 840 400" stroke="#7f8c8d" stroke-width="4" fill="none" marker-end="url(#arrowhead1)"/>
    <path d="M 1080 400 L 1440 400" stroke="#7f8c8d" stroke-width="4" fill="none" marker-end="url(#arrowhead2)"/>
  </g>
  
  <!-- 底部金句 -->
  <g transform="translate(960, 700)">
    <!-- 背景装饰 -->
    <rect x="-500" y="-50" width="1000" height="100" fill="#f8f9fa" rx="20"/>
    <rect x="-480" y="-30" width="960" height="60" fill="#e9ecef" rx="15"/>
    
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="36" font-weight="bold" fill="#e74c3c">
      "课程大纲"传递的是信息，而"课程框架"，传递的是信任。
    </text>
  </g>
  
  <!-- 装饰性地图元素 -->
  <g opacity="0.2">
    <!-- 左上角指南针 -->
    <g transform="translate(200, 200)">
      <circle cx="0" cy="0" r="30" fill="#34495e"/>
      <circle cx="0" cy="0" r="25" fill="#2c3e50"/>
      <path d="M 0 -20 L -8 8 L 0 0 L 8 8 Z" fill="#e74c3c"/>
      <path d="M 0 20 L -8 -8 L 0 0 L 8 -8 Z" fill="#ffffff"/>
      <text x="0" y="50" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="12" fill="#7f8c8d">
        N
      </text>
    </g>
    
    <!-- 右下角地图图例 -->
    <g transform="translate(1720, 800)">
      <rect x="-50" y="-40" width="100" height="80" fill="#ecf0f1" rx="5"/>
      <circle cx="-30" cy="-20" r="4" fill="#e74c3c"/>
      <text x="-15" y="-15" font-family="Microsoft YaHei, sans-serif" font-size="10" fill="#2c3e50">起点</text>
      <circle cx="-30" cy="0" r="4" fill="#f39c12"/>
      <text x="-15" y="5" font-family="Microsoft YaHei, sans-serif" font-size="10" fill="#2c3e50">中转</text>
      <circle cx="-30" cy="20" r="4" fill="#27ae60"/>
      <text x="-15" y="25" font-family="Microsoft YaHei, sans-serif" font-size="10" fill="#2c3e50">终点</text>
    </g>
  </g>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="arrowhead1" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#7f8c8d"/>
    </marker>
    <marker id="arrowhead2" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#7f8c8d"/>
    </marker>
  </defs>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.3">
    <path d="M 100 950 Q 960 900 1820 950" stroke="#e74c3c" stroke-width="4" fill="none"/>
  </g>
</svg>
