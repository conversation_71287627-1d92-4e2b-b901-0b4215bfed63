<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <g transform="translate(960, 120)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
      要素三：最小化行动 (The Minimum Viable Action)
    </text>
  </g>
  
  <!-- 主视觉：复杂任务清单被划掉，简单第一步被勾选 -->
  <g transform="translate(960, 400)">
    <!-- 复杂任务清单（被划掉） -->
    <g transform="translate(-250, 0)">
      <rect x="-150" y="-150" width="300" height="300" fill="#ffffff" stroke="#e74c3c" stroke-width="3" rx="10"/>
      <rect x="-140" y="-140" width="280" height="280" fill="#ffebee" rx="8"/>
      
      <!-- 清单标题 -->
      <text x="0" y="-110" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" font-weight="bold" fill="#e74c3c">
        复杂任务清单
      </text>
      
      <!-- 任务项目 -->
      <g opacity="0.6">
        <rect x="-120" y="-80" width="240" height="15" fill="#bdc3c7" rx="3"/>
        <rect x="-120" y="-55" width="200" height="15" fill="#bdc3c7" rx="3"/>
        <rect x="-120" y="-30" width="220" height="15" fill="#bdc3c7" rx="3"/>
        <rect x="-120" y="-5" width="180" height="15" fill="#bdc3c7" rx="3"/>
        <rect x="-120" y="20" width="210" height="15" fill="#bdc3c7" rx="3"/>
        <rect x="-120" y="45" width="190" height="15" fill="#bdc3c7" rx="3"/>
        <rect x="-120" y="70" width="230" height="15" fill="#bdc3c7" rx="3"/>
        <rect x="-120" y="95" width="170" height="15" fill="#bdc3c7" rx="3"/>
      </g>
      
      <!-- 大红叉 -->
      <g>
        <line x1="-100" y1="-100" x2="100" y2="100" stroke="#e74c3c" stroke-width="8"/>
        <line x1="100" y1="-100" x2="-100" y2="100" stroke="#e74c3c" stroke-width="8"/>
      </g>
    </g>
    
    <!-- 简单第一步（被勾选） -->
    <g transform="translate(250, 0)">
      <rect x="-120" y="-80" width="240" height="160" fill="#ffffff" stroke="#27ae60" stroke-width="3" rx="10"/>
      <rect x="-110" y="-70" width="220" height="140" fill="#e8f5e8" rx="8"/>
      
      <!-- 标题 -->
      <text x="0" y="-40" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" font-weight="bold" fill="#27ae60">
        第一步
      </text>
      
      <!-- 简单任务 -->
      <rect x="-80" y="-10" width="160" height="20" fill="#c8e6c9" rx="5"/>
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#2c3e50">
        简单、具体的微行动
      </text>
      
      <!-- 大绿勾 -->
      <g transform="translate(0, 40)">
        <circle cx="0" cy="0" r="25" fill="#27ae60"/>
        <path d="M -12 0 L -5 7 L 12 -10" stroke="#ffffff" stroke-width="4" fill="none"/>
      </g>
    </g>
    
    <!-- 箭头指向 -->
    <g transform="translate(0, 0)">
      <path d="M -100 0 L 100 0 M 85 -15 L 100 0 L 85 15" stroke="#f39c12" stroke-width="6" fill="none"/>
      <text x="0" y="-30" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="20" font-weight="bold" fill="#f39c12">
        简化
      </text>
    </g>
  </g>
  
  <!-- 核心要点 -->
  <g transform="translate(960, 650)">
    <rect x="-450" y="-80" width="900" height="160" fill="#fef5e7" rx="20"/>
    
    <text x="0" y="-40" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" font-weight="bold" fill="#2c3e50">
      目的：降低门槛，启动飞轮。
    </text>
    <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="20" fill="#e74c3c">
      错误做法："回去写一份详细的行动计划。"（任务过重）
    </text>
    <text x="0" y="20" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="20" fill="#27ae60">
      正确做法：布置一个极其简单、极其具体的"微行动"。
    </text>
  </g>
  
  <!-- 行动设计两大法则 -->
  <g transform="translate(960, 800)">
    <rect x="-300" y="-40" width="600" height="80" fill="#3498db" rx="20"/>
    
    <text x="-100" y="-5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" font-weight="bold" fill="#ffffff">
      10分钟内能完成
    </text>
    <text x="100" y="-5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" font-weight="bold" fill="#ffffff">
      24小时内能启动
    </text>
    
    <!-- 分隔线 -->
    <line x1="0" y1="-30" x2="0" y2="30" stroke="#ffffff" stroke-width="3"/>
  </g>
  
  <!-- 装饰性行动元素 -->
  <g opacity="0.2">
    <!-- 左侧齿轮 -->
    <g transform="translate(200, 500)">
      <circle cx="0" cy="0" r="25" fill="#3498db"/>
      <circle cx="0" cy="0" r="18" fill="#2980b9"/>
      <circle cx="0" cy="0" r="8" fill="#1f4e79"/>
      
      <!-- 齿 -->
      <rect x="-3" y="-30" width="6" height="10" fill="#3498db"/>
      <rect x="-3" y="20" width="6" height="10" fill="#3498db"/>
      <rect x="-30" y="-3" width="10" height="6" fill="#3498db"/>
      <rect x="20" y="-3" width="10" height="6" fill="#3498db"/>
    </g>
    
    <!-- 右侧计时器 -->
    <g transform="translate(1720, 450)">
      <circle cx="0" cy="0" r="20" fill="#f39c12"/>
      <circle cx="0" cy="0" r="15" fill="#e67e22"/>
      
      <!-- 指针 -->
      <line x1="0" y1="0" x2="0" y2="-10" stroke="#ffffff" stroke-width="2"/>
      <line x1="0" y1="0" x2="7" y2="0" stroke="#ffffff" stroke-width="2"/>
      <circle cx="0" cy="0" r="2" fill="#ffffff"/>
    </g>
  </g>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.3">
    <path d="M 100 950 Q 960 900 1820 950" stroke="#3498db" stroke-width="4" fill="none"/>
  </g>
</svg>
