<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <g transform="translate(960, 150)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
      收场的唯一价值：不是"回顾"，而是"启动"
    </text>
  </g>
  
  <!-- 主视觉：巨大的红色发射按钮 -->
  <g transform="translate(960, 450)">
    <!-- 按钮底座 -->
    <ellipse cx="0" cy="50" rx="150" ry="30" fill="#7f8c8d"/>
    <ellipse cx="0" cy="45" rx="140" ry="25" fill="#95a5a6"/>
    
    <!-- 按钮主体 -->
    <circle cx="0" cy="0" r="120" fill="#e74c3c"/>
    <circle cx="0" cy="0" r="100" fill="#c0392b"/>
    <circle cx="0" cy="0" r="80" fill="#e74c3c"/>
    
    <!-- 按钮表面光泽 -->
    <ellipse cx="-30" cy="-30" rx="40" ry="20" fill="#ffffff" opacity="0.3"/>
    <ellipse cx="-25" cy="-25" rx="30" ry="15" fill="#ffffff" opacity="0.5"/>
    
    <!-- 按钮文字 -->
    <text x="0" y="10" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="32" font-weight="bold" fill="#ffffff">
      启动
    </text>
    
    <!-- 准备按下的手 -->
    <g transform="translate(0, -150)">
      <!-- 手掌 -->
      <ellipse cx="0" cy="0" rx="40" ry="25" fill="#f39c12"/>
      <ellipse cx="0" cy="0" rx="35" ry="20" fill="#e67e22"/>
      
      <!-- 手指 -->
      <ellipse cx="-20" cy="-15" rx="8" ry="20" fill="#f39c12"/>
      <ellipse cx="-5" cy="-20" rx="8" ry="25" fill="#f39c12"/>
      <ellipse cx="10" cy="-20" rx="8" ry="25" fill="#f39c12"/>
      <ellipse cx="25" cy="-15" rx="8" ry="20" fill="#f39c12"/>
      
      <!-- 拇指 -->
      <ellipse cx="-35" cy="5" rx="12" ry="18" fill="#f39c12" transform="rotate(-30 -35 5)"/>
      
      <!-- 手臂 -->
      <rect x="-15" y="25" width="30" height="60" fill="#f39c12" rx="15"/>
      <rect x="-12" y="28" width="24" height="54" fill="#e67e22" rx="12"/>
      
      <!-- 动作线条 -->
      <g opacity="0.6">
        <path d="M -60 -40 Q -40 -60 -20 -40" stroke="#f1c40f" stroke-width="3" fill="none"/>
        <path d="M 60 -40 Q 40 -60 20 -40" stroke="#f1c40f" stroke-width="3" fill="none"/>
        <path d="M 0 -80 Q -20 -100 0 -120" stroke="#f1c40f" stroke-width="3" fill="none"/>
      </g>
    </g>
    
    <!-- 能量波纹 -->
    <g opacity="0.4">
      <circle cx="0" cy="0" r="150" fill="none" stroke="#e74c3c" stroke-width="4"/>
      <circle cx="0" cy="0" r="180" fill="none" stroke="#e74c3c" stroke-width="3"/>
      <circle cx="0" cy="0" r="210" fill="none" stroke="#e74c3c" stroke-width="2"/>
    </g>
  </g>
  
  <!-- 核心文字 -->
  <g transform="translate(960, 700)">
    <rect x="-200" y="-40" width="400" height="80" fill="#e74c3c" rx="40"/>
    <text x="0" y="10" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="48" font-weight="bold" fill="#ffffff">
      促使行动！
    </text>
  </g>
  
  <!-- 底部说明 -->
  <g transform="translate(960, 850)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="32" fill="#7f8c8d">
      你的收场，不是一次"复习"，而是一场"出征仪式"！
    </text>
  </g>
  
  <!-- 装饰性启动元素 -->
  <g opacity="0.2">
    <!-- 左侧火箭 -->
    <g transform="translate(200, 600)">
      <path d="M 0 -30 L -15 0 L 15 0 Z" fill="#e74c3c"/>
      <rect x="-10" y="0" width="20" height="40" fill="#34495e" rx="3"/>
      <rect x="-15" y="40" width="30" height="20" fill="#2c3e50" rx="5"/>
      
      <!-- 火焰 -->
      <g transform="translate(0, 60)">
        <path d="M -10 0 Q 0 -20 10 0" fill="#f39c12"/>
        <path d="M -5 0 Q 0 -15 5 0" fill="#f1c40f"/>
      </g>
    </g>
    
    <!-- 右侧齿轮 -->
    <g transform="translate(1720, 500)">
      <circle cx="0" cy="0" r="25" fill="#34495e"/>
      <circle cx="0" cy="0" r="18" fill="#2c3e50"/>
      <circle cx="0" cy="0" r="8" fill="#7f8c8d"/>
      
      <!-- 齿 -->
      <rect x="-3" y="-30" width="6" height="10" fill="#34495e"/>
      <rect x="-3" y="20" width="6" height="10" fill="#34495e"/>
      <rect x="-30" y="-3" width="10" height="6" fill="#34495e"/>
      <rect x="20" y="-3" width="10" height="6" fill="#34495e"/>
    </g>
  </g>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.3">
    <path d="M 100 950 Q 960 900 1820 950" stroke="#e74c3c" stroke-width="4" fill="none"/>
  </g>
</svg>
