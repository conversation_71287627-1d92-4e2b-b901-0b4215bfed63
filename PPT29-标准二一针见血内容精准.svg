<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <g transform="translate(960, 120)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
      标准二：一针见血 (内容精准)
    </text>
  </g>
  
  <!-- 手术刀图标 -->
  <g transform="translate(960, 300)">
    <circle cx="0" cy="0" r="80" fill="#3498db" opacity="0.2"/>
    
    <!-- 手术刀 -->
    <g transform="translate(0, -20)">
      <rect x="-4" y="-50" width="8" height="80" fill="#34495e" rx="2"/>
      <path d="M 0 -50 L -20 -70 L 20 -70 Z" fill="#ecf0f1"/>
      <path d="M 0 -50 L -15 -65 L 15 -65 Z" fill="#bdc3c7"/>
      <rect x="-10" y="30" width="20" height="20" fill="#3498db" rx="3"/>
      
      <!-- 锋利光效 -->
      <path d="M -12 -65 L 12 -65" stroke="#ffffff" stroke-width="1" opacity="0.8"/>
      <path d="M -10 -62 L 10 -62" stroke="#ffffff" stroke-width="0.5" opacity="0.6"/>
    </g>
  </g>
  
  <!-- 灵魂拷问对话框 -->
  <g transform="translate(960, 500)">
    <!-- 对话框背景 -->
    <rect x="-400" y="-80" width="800" height="160" fill="#f8f9fa" rx="20"/>
    <rect x="-380" y="-60" width="760" height="120" fill="#e9ecef" rx="15"/>
    
    <!-- 对话框尖角 -->
    <path d="M -50 80 L 0 100 L 50 80 Z" fill="#e9ecef"/>
    
    <!-- 引号装饰 -->
    <text x="-350" y="-20" font-family="Microsoft YaHei, sans-serif" font-size="48" fill="#3498db" opacity="0.3">
      "
    </text>
    <text x="350" y="40" font-family="Microsoft YaHei, sans-serif" font-size="48" fill="#3498db" opacity="0.3">
      "
    </text>
    
    <!-- 问题内容 -->
    <text x="0" y="-20" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="32" font-weight="bold" fill="#e74c3c">
      "如果砍掉这一步，我的学员还能不能抵达终点？"
    </text>
    
    <!-- 标签 -->
    <g transform="translate(0, 40)">
      <rect x="-80" y="-15" width="160" height="30" fill="#3498db" rx="15"/>
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="16" font-weight="bold" fill="#ffffff">
        灵魂拷问
      </text>
    </g>
  </g>
  
  <!-- 结论 -->
  <g transform="translate(960, 700)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="36" font-weight="bold" fill="#2c3e50">
      每一个模块，都必须是为你的"钉子"服务的"必要步骤"。
    </text>
  </g>
  
  <!-- 装饰性删除元素 -->
  <g opacity="0.3">
    <!-- 左侧垃圾桶 -->
    <g transform="translate(200, 600)">
      <rect x="-15" y="-20" width="30" height="40" fill="#e74c3c" rx="5"/>
      <rect x="-12" y="-15" width="24" height="30" fill="#c0392b" rx="3"/>
      <rect x="-18" y="-25" width="36" height="8" fill="#e74c3c" rx="4"/>
      <circle cx="-8" cy="-21" r="2" fill="#ffffff"/>
      <circle cx="8" cy="-21" r="2" fill="#ffffff"/>
      
      <!-- 垃圾桶内容线条 -->
      <line x1="-8" y1="-10" x2="-8" y2="10" stroke="#ffffff" stroke-width="2"/>
      <line x1="0" y1="-10" x2="0" y2="10" stroke="#ffffff" stroke-width="2"/>
      <line x1="8" y1="-10" x2="8" y2="10" stroke="#ffffff" stroke-width="2"/>
    </g>
    
    <!-- 右侧剪刀 -->
    <g transform="translate(1720, 600)">
      <g transform="rotate(45)">
        <ellipse cx="-10" cy="0" rx="8" ry="15" fill="#95a5a6"/>
        <ellipse cx="10" cy="0" rx="8" ry="15" fill="#95a5a6"/>
        <line x1="-15" y1="0" x2="15" y2="0" stroke="#7f8c8d" stroke-width="3"/>
        <circle cx="0" cy="0" r="4" fill="#34495e"/>
      </g>
    </g>
  </g>
  
  <!-- 装饰性精准元素 -->
  <g transform="translate(300, 300)" opacity="0.2">
    <!-- 靶心 -->
    <circle cx="0" cy="0" r="30" fill="#e74c3c"/>
    <circle cx="0" cy="0" r="20" fill="#ffffff"/>
    <circle cx="0" cy="0" r="10" fill="#e74c3c"/>
    <circle cx="0" cy="0" r="5" fill="#ffffff"/>
  </g>
  
  <g transform="translate(1620, 400)" opacity="0.2">
    <!-- 精准指针 -->
    <line x1="0" y1="-30" x2="0" y2="30" stroke="#3498db" stroke-width="4"/>
    <path d="M 0 -30 L -8 -20 L 8 -20 Z" fill="#3498db"/>
    <circle cx="0" cy="0" r="6" fill="#2980b9"/>
  </g>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.3">
    <path d="M 100 950 Q 960 900 1820 950" stroke="#3498db" stroke-width="4" fill="none"/>
  </g>
</svg>
