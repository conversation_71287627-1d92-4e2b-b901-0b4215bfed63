<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <g transform="translate(960, 120)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
      "钉子定位"三步法
    </text>
  </g>
  
  <!-- 三步流程 -->
  <!-- Step 1: 定领域 -->
  <g transform="translate(320, 400)">
    <!-- 背景圆形 -->
    <circle cx="0" cy="0" r="150" fill="#e74c3c" opacity="0.1"/>
    <circle cx="0" cy="0" r="120" fill="#c0392b" opacity="0.1"/>
    
    <!-- 步骤编号 -->
    <circle cx="0" cy="-180" r="30" fill="#e74c3c"/>
    <text x="0" y="-170" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" font-weight="bold" fill="#ffffff">
      1
    </text>
    
    <!-- 标题 -->
    <text x="0" y="-120" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="36" font-weight="bold" fill="#e74c3c">
      定领域
    </text>
    
    <!-- 副标题 -->
    <text x="0" y="-80" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" fill="#2c3e50">
      宁为鸡首，不为牛后。
    </text>
    <text x="0" y="-50" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="22" fill="#7f8c8d">
      找到你的"黄金交叉点"。
    </text>
    
    <!-- 坐标轴图标 -->
    <g transform="translate(0, 30)">
      <line x1="-60" y1="0" x2="60" y2="0" stroke="#e74c3c" stroke-width="4"/>
      <line x1="0" y1="-60" x2="0" y2="60" stroke="#e74c3c" stroke-width="4"/>
      <path d="M 50 -10 L 60 0 L 50 10" stroke="#e74c3c" stroke-width="3" fill="none"/>
      <path d="M -10 -50 L 0 -60 L 10 -50" stroke="#e74c3c" stroke-width="3" fill="none"/>
      
      <!-- 交叉点 -->
      <circle cx="20" cy="-20" r="8" fill="#f39c12"/>
      <text x="35" y="-35" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#f39c12">
        黄金点
      </text>
    </g>
  </g>
  
  <!-- Step 2: 定客户 -->
  <g transform="translate(960, 400)">
    <!-- 背景圆形 -->
    <circle cx="0" cy="0" r="150" fill="#3498db" opacity="0.1"/>
    <circle cx="0" cy="0" r="120" fill="#2980b9" opacity="0.1"/>
    
    <!-- 步骤编号 -->
    <circle cx="0" cy="-180" r="30" fill="#3498db"/>
    <text x="0" y="-170" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" font-weight="bold" fill="#ffffff">
      2
    </text>
    
    <!-- 标题 -->
    <text x="0" y="-120" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="36" font-weight="bold" fill="#3498db">
      定客户
    </text>
    
    <!-- 副标题 -->
    <text x="0" y="-80" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" fill="#2c3e50">
      为唯一的"英雄"画像。
    </text>
    
    <!-- 人物头像剪影 -->
    <g transform="translate(0, 20)">
      <circle cx="0" cy="-20" r="35" fill="#3498db"/>
      <circle cx="0" cy="-25" r="15" fill="#ffffff"/>
      <path d="M -25 10 Q 0 -10 25 10 Q 25 40 0 50 Q -25 40 -25 10 Z" fill="#ffffff"/>
      
      <!-- 特征标签 -->
      <rect x="-40" y="60" width="80" height="20" fill="#3498db" rx="10"/>
      <text x="0" y="73" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="12" fill="#ffffff">
        目标用户画像
      </text>
    </g>
  </g>
  
  <!-- Step 3: 定一痛 -->
  <g transform="translate(1600, 400)">
    <!-- 背景圆形 -->
    <circle cx="0" cy="0" r="150" fill="#f39c12" opacity="0.1"/>
    <circle cx="0" cy="0" r="120" fill="#e67e22" opacity="0.1"/>
    
    <!-- 步骤编号 -->
    <circle cx="0" cy="-180" r="30" fill="#f39c12"/>
    <text x="0" y="-170" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" font-weight="bold" fill="#ffffff">
      3
    </text>
    
    <!-- 标题 -->
    <text x="0" y="-120" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="36" font-weight="bold" fill="#f39c12">
      定一痛
    </text>
    
    <!-- 副标题 -->
    <text x="0" y="-80" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" fill="#2c3e50">
      从"万能药"到"手术刀"。
    </text>
    
    <!-- 手术刀图标 -->
    <g transform="translate(0, 20)">
      <rect x="-3" y="-40" width="6" height="80" fill="#34495e" rx="1"/>
      <path d="M 0 -40 L -15 -55 L 15 -55 Z" fill="#ecf0f1"/>
      <path d="M 0 -40 L -12 -52 L 12 -52 Z" fill="#bdc3c7"/>
      <rect x="-8" y="40" width="16" height="15" fill="#f39c12" rx="2"/>
      
      <!-- 锋利光效 -->
      <path d="M -10 -52 L 10 -52" stroke="#ffffff" stroke-width="1" opacity="0.8"/>
      <path d="M -8 -50 L 8 -50" stroke="#ffffff" stroke-width="0.5" opacity="0.6"/>
    </g>
  </g>
  
  <!-- 连接箭头 -->
  <g opacity="0.6">
    <path d="M 470 400 L 810 400" stroke="#7f8c8d" stroke-width="4" fill="none" marker-end="url(#arrowhead1)"/>
    <path d="M 1110 400 L 1450 400" stroke="#7f8c8d" stroke-width="4" fill="none" marker-end="url(#arrowhead2)"/>
  </g>
  
  <!-- 核心工具 -->
  <g transform="translate(960, 750)">
    <!-- 工具箱图标 -->
    <g transform="translate(0, -50)">
      <rect x="-60" y="-30" width="120" height="60" fill="#9b59b6" rx="10"/>
      <rect x="-50" y="-20" width="100" height="40" fill="#8e44ad" rx="5"/>
      <circle cx="-20" cy="0" r="8" fill="#ecf0f1"/>
      <circle cx="0" cy="0" r="8" fill="#ecf0f1"/>
      <circle cx="20" cy="0" r="8" fill="#ecf0f1"/>
      
      <!-- 工具箱把手 -->
      <rect x="-15" y="-40" width="30" height="10" fill="#34495e" rx="5"/>
    </g>
    
    <text x="0" y="30" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="32" font-weight="bold" fill="#9b59b6">
      核心工具：5 Why 提问法
    </text>
  </g>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="arrowhead1" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#7f8c8d"/>
    </marker>
    <marker id="arrowhead2" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#7f8c8d"/>
    </marker>
  </defs>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.3">
    <path d="M 100 950 Q 960 900 1820 950" stroke="#9b59b6" stroke-width="4" fill="none"/>
  </g>
</svg>
