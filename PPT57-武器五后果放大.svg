<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <g transform="translate(960, 120)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
      武器五：后果放大
    </text>
  </g>
  
  <!-- 主视觉：多米诺骨牌连锁倒塌 -->
  <g transform="translate(960, 350)">
    <!-- 多米诺骨牌序列 -->
    <g>
      <!-- 第一块（已倒下） -->
      <g transform="translate(-200, 0)">
        <rect x="-8" y="-30" width="16" height="60" fill="#e74c3c" rx="3" transform="rotate(60)"/>
        <rect x="-6" y="-25" width="12" height="50" fill="#c0392b" rx="2" transform="rotate(60)"/>
      </g>
      
      <!-- 第二块（正在倒下） -->
      <g transform="translate(-100, 0)">
        <rect x="-8" y="-30" width="16" height="60" fill="#f39c12" rx="3" transform="rotate(30)"/>
        <rect x="-6" y="-25" width="12" height="50" fill="#e67e22" rx="2" transform="rotate(30)"/>
      </g>
      
      <!-- 第三块（即将倒下） -->
      <g transform="translate(0, 0)">
        <rect x="-8" y="-30" width="16" height="60" fill="#f1c40f" rx="3" transform="rotate(10)"/>
        <rect x="-6" y="-25" width="12" height="50" fill="#f39c12" rx="2" transform="rotate(10)"/>
      </g>
      
      <!-- 第四块（直立但摇摆） -->
      <g transform="translate(100, 0)">
        <rect x="-8" y="-30" width="16" height="60" fill="#27ae60" rx="3" transform="rotate(2)"/>
        <rect x="-6" y="-25" width="12" height="50" fill="#2ecc71" rx="2" transform="rotate(2)"/>
      </g>
      
      <!-- 第五块（直立） -->
      <g transform="translate(200, 0)">
        <rect x="-8" y="-30" width="16" height="60" fill="#3498db" rx="3"/>
        <rect x="-6" y="-25" width="12" height="50" fill="#2980b9" rx="2"/>
      </g>
      
      <!-- 更多骨牌（远景） -->
      <g opacity="0.6">
        <rect x="292" y="-30" width="16" height="60" fill="#9b59b6" rx="3"/>
        <rect x="384" y="-30" width="16" height="60" fill="#8e44ad" rx="3"/>
        <rect x="476" y="-30" width="16" height="60" fill="#95a5a6" rx="3"/>
      </g>
    </g>
    
    <!-- 冲击波和运动线 -->
    <g opacity="0.6">
      <!-- 冲击波 -->
      <circle cx="-200" cy="0" r="50" fill="none" stroke="#e74c3c" stroke-width="3"/>
      <circle cx="-200" cy="0" r="80" fill="none" stroke="#e74c3c" stroke-width="2"/>
      <circle cx="-200" cy="0" r="110" fill="none" stroke="#e74c3c" stroke-width="1"/>
      
      <!-- 运动线 -->
      <path d="M -250 -20 Q -200 -10 -150 -20" stroke="#f39c12" stroke-width="3" fill="none"/>
      <path d="M -150 -15 Q -100 -5 -50 -15" stroke="#f1c40f" stroke-width="3" fill="none"/>
      <path d="M -50 -10 Q 0 0 50 -10" stroke="#27ae60" stroke-width="3" fill="none"/>
      <path d="M 50 -5 Q 100 5 150 -5" stroke="#3498db" stroke-width="3" fill="none"/>
    </g>
    
    <!-- 灾难性后果标识 -->
    <g transform="translate(300, -100)" opacity="0.8">
      <circle cx="0" cy="0" r="40" fill="#e74c3c"/>
      <circle cx="0" cy="0" r="30" fill="#c0392b"/>
      <path d="M -15 -15 L 15 15 M 15 -15 L -15 15" stroke="#ffffff" stroke-width="4"/>
      
      <!-- 爆炸效果 -->
      <g opacity="0.6">
        <line x1="0" y1="-60" x2="0" y2="-80" stroke="#f39c12" stroke-width="4"/>
        <line x1="42" y1="-42" x2="56" y2="-56" stroke="#f39c12" stroke-width="4"/>
        <line x1="60" y1="0" x2="80" y2="0" stroke="#f39c12" stroke-width="4"/>
        <line x1="42" y1="42" x2="56" y2="56" stroke="#f39c12" stroke-width="4"/>
        <line x1="0" y1="60" x2="0" y2="80" stroke="#f39c12" stroke-width="4"/>
        <line x1="-42" y1="42" x2="-56" y2="56" stroke="#f39c12" stroke-width="4"/>
        <line x1="-60" y1="0" x2="-80" y2="0" stroke="#f39c12" stroke-width="4"/>
        <line x1="-42" y1="-42" x2="-56" y2="-56" stroke="#f39c12" stroke-width="4"/>
      </g>
    </g>
  </g>
  
  <!-- 核心句式 -->
  <g transform="translate(960, 550)">
    <rect x="-500" y="-40" width="1000" height="80" fill="#f8f9fa" rx="20"/>
    <rect x="-480" y="-20" width="960" height="40" fill="#e9ecef" rx="15"/>
    
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="26" font-weight="bold" fill="#9b59b6">
      "如果我们继续...，我们看似是在...，但实际上，我们是在亲手..."
    </text>
  </g>
  
  <!-- 案例 -->
  <g transform="translate(960, 700)">
    <!-- 案例框 -->
    <rect x="-450" y="-100" width="900" height="200" fill="#fef5e7" rx="20"/>
    <rect x="-430" y="-80" width="860" height="160" fill="#fdeaa7" opacity="0.5" rx="15"/>
    
    <!-- 案例标题 -->
    <text x="0" y="-50" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" font-weight="bold" fill="#f39c12">
      案例：商业竞争
    </text>
    
    <!-- 旧神 -->
    <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="22" fill="#2c3e50">
      旧神：打折促销能吸引客户。
    </text>
    
    <!-- 攻击 -->
    <text x="0" y="20" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="19" fill="#e74c3c">
      攻击：你是在筛选最劣质的用户，最终会让你失去定价权，
    </text>
    <text x="0" y="50" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="19" fill="#e74c3c">
      沦为行业最累的炮灰。
    </text>
  </g>
  
  <!-- 装饰性连锁反应元素 -->
  <g opacity="0.2">
    <!-- 左侧连锁箭头 -->
    <g transform="translate(200, 400)">
      <path d="M 0 0 L 30 0" stroke="#e74c3c" stroke-width="4" marker-end="url(#arrowhead1)"/>
      <path d="M 40 0 L 70 0" stroke="#f39c12" stroke-width="4" marker-end="url(#arrowhead2)"/>
      <path d="M 80 0 L 110 0" stroke="#27ae60" stroke-width="4" marker-end="url(#arrowhead3)"/>
    </g>
    
    <!-- 右侧警告链 -->
    <g transform="translate(1620, 500)">
      <circle cx="0" cy="0" r="8" fill="#e74c3c"/>
      <line x1="8" y1="0" x2="22" y2="0" stroke="#e74c3c" stroke-width="3"/>
      <circle cx="30" cy="0" r="8" fill="#f39c12"/>
      <line x1="38" y1="0" x2="52" y2="0" stroke="#f39c12" stroke-width="3"/>
      <circle cx="60" cy="0" r="8" fill="#27ae60"/>
    </g>
  </g>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="arrowhead1" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c"/>
    </marker>
    <marker id="arrowhead2" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#f39c12"/>
    </marker>
    <marker id="arrowhead3" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#27ae60"/>
    </marker>
  </defs>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.3">
    <path d="M 100 950 Q 960 900 1820 950" stroke="#9b59b6" stroke-width="4" fill="none"/>
  </g>
</svg>
