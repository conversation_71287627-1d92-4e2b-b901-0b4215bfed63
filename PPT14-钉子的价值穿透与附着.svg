<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <g transform="translate(960, 120)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
      "钉子"的价值：穿透与附着
    </text>
  </g>
  
  <!-- 左侧：穿透性 -->
  <g transform="translate(480, 400)">
    <!-- 背景区域 -->
    <rect x="-350" y="-200" width="700" height="400" fill="#e8f6f3" rx="20"/>
    
    <!-- 钉子穿透墙的视觉 -->
    <g transform="translate(0, -50)">
      <!-- 墙 -->
      <rect x="-150" y="-80" width="300" height="160" fill="#95a5a6" rx="10"/>
      <rect x="-140" y="-70" width="280" height="140" fill="#bdc3c7" rx="8"/>
      
      <!-- 钉子 -->
      <rect x="-6" y="-100" width="12" height="200" fill="#e74c3c" rx="2"/>
      <circle cx="0" cy="-110" r="12" fill="#c0392b"/>
      
      <!-- 穿透效果 -->
      <g opacity="0.7">
        <circle cx="0" cy="0" r="30" fill="#e74c3c" opacity="0.3"/>
        <circle cx="0" cy="0" r="20" fill="#e74c3c" opacity="0.5"/>
        <circle cx="0" cy="0" r="10" fill="#e74c3c" opacity="0.8"/>
      </g>
      
      <!-- 冲击波 -->
      <g opacity="0.4">
        <circle cx="0" cy="0" r="50" fill="none" stroke="#e74c3c" stroke-width="3"/>
        <circle cx="0" cy="0" r="70" fill="none" stroke="#e74c3c" stroke-width="2"/>
        <circle cx="0" cy="0" r="90" fill="none" stroke="#e74c3c" stroke-width="1"/>
      </g>
    </g>
    
    <!-- 标题 -->
    <text x="0" y="120" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="36" font-weight="bold" fill="#e74c3c">
      穿透性
    </text>
    
    <!-- 描述 -->
    <text x="0" y="160" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" fill="#2c3e50">
      瞬间穿过"信息过滤网"，
    </text>
    <text x="0" y="190" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" fill="#2c3e50">
      让用户意识到"这跟我有关！"
    </text>
  </g>
  
  <!-- 右侧：附着力 -->
  <g transform="translate(1440, 400)">
    <!-- 背景区域 -->
    <rect x="-350" y="-200" width="700" height="400" fill="#fef9e7" rx="20"/>
    
    <!-- 钉子挂重物的视觉 -->
    <g transform="translate(0, -50)">
      <!-- 墙 -->
      <rect x="-150" y="-80" width="300" height="160" fill="#95a5a6" rx="10"/>
      <rect x="-140" y="-70" width="280" height="140" fill="#bdc3c7" rx="8"/>
      
      <!-- 钉子 -->
      <rect x="-6" y="-100" width="12" height="120" fill="#f39c12" rx="2"/>
      <circle cx="0" cy="-110" r="12" fill="#e67e22"/>
      
      <!-- 挂着的重物 -->
      <rect x="-40" y="40" width="80" height="60" fill="#3498db" rx="5"/>
      <rect x="-35" y="45" width="70" height="50" fill="#2980b9" rx="3"/>
      <text x="0" y="75" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#ffffff">
        记忆
      </text>
      
      <!-- 连接线 -->
      <line x1="0" y1="20" x2="0" y2="40" stroke="#34495e" stroke-width="3"/>
    </g>
    
    <!-- 标题 -->
    <text x="0" y="120" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="36" font-weight="bold" fill="#f39c12">
      附着力
    </text>
    
    <!-- 描述 -->
    <text x="0" y="160" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" fill="#2c3e50">
      一旦进入用户大脑，
    </text>
    <text x="0" y="190" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" fill="#2c3e50">
      就能牢牢挂住，挥之不去。
    </text>
  </g>
  
  <!-- 底部品牌案例 -->
  <g transform="translate(960, 750)">
    <!-- 王老吉案例 -->
    <g transform="translate(-300, 0)">
      <circle cx="0" cy="0" r="60" fill="#e74c3c" opacity="0.1"/>
      <rect x="-50" y="-30" width="100" height="60" fill="#e74c3c" rx="30"/>
      <text x="0" y="-5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="16" font-weight="bold" fill="#ffffff">
        王老吉
      </text>
      <text x="0" y="15" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="12" fill="#ffffff">
        LOGO
      </text>
      
      <text x="0" y="100" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="20" font-weight="bold" fill="#e74c3c">
        "怕上火，喝王老吉。"
      </text>
    </g>
    
    <!-- 农夫山泉案例 -->
    <g transform="translate(300, 0)">
      <circle cx="0" cy="0" r="60" fill="#3498db" opacity="0.1"/>
      <rect x="-50" y="-30" width="100" height="60" fill="#3498db" rx="30"/>
      <text x="0" y="-5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="14" font-weight="bold" fill="#ffffff">
        农夫山泉
      </text>
      <text x="0" y="15" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="12" fill="#ffffff">
        LOGO
      </text>
      
      <text x="0" y="100" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" font-weight="bold" fill="#3498db">
        "我们不生产水，我们只是大自然的搬运工。"
      </text>
    </g>
  </g>
  
  <!-- 装饰性箭头 -->
  <g opacity="0.3">
    <path d="M 200 300 L 350 300" stroke="#e74c3c" stroke-width="4" fill="none" marker-end="url(#arrowhead1)"/>
    <path d="M 1570 300 L 1720 300" stroke="#f39c12" stroke-width="4" fill="none" marker-end="url(#arrowhead2)"/>
  </g>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="arrowhead1" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c"/>
    </marker>
    <marker id="arrowhead2" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#f39c12"/>
    </marker>
  </defs>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.2">
    <path d="M 100 950 Q 960 900 1820 950" stroke="#e74c3c" stroke-width="4" fill="none"/>
  </g>
</svg>
