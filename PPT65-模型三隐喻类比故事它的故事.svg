<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <g transform="translate(960, 120)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
      模型三：隐喻/类比故事 (它的故事)
    </text>
  </g>
  
  <!-- 主视觉：老师傅用茶杯茶壶讲解道理 -->
  <g transform="translate(960, 400)">
    <!-- 茶桌背景 -->
    <g opacity="0.3">
      <ellipse cx="0" cy="100" rx="300" ry="80" fill="#8e44ad"/>
      <ellipse cx="0" cy="95" rx="280" ry="70" fill="#9b59b6"/>
    </g>
    
    <!-- 老师傅 -->
    <g transform="translate(-150, -50)">
      <!-- 头部 -->
      <circle cx="0" cy="-30" r="25" fill="#f39c12"/>
      <circle cx="0" cy="-30" r="20" fill="#e67e22"/>
      
      <!-- 胡须 -->
      <g opacity="0.8">
        <path d="M -15 -15 Q -25 -10 -20 -5" stroke="#bdc3c7" stroke-width="3" fill="none"/>
        <path d="M 0 -10 Q -5 -5 0 0" stroke="#bdc3c7" stroke-width="3" fill="none"/>
        <path d="M 15 -15 Q 25 -10 20 -5" stroke="#bdc3c7" stroke-width="3" fill="none"/>
      </g>
      
      <!-- 身体 -->
      <rect x="-20" y="-5" width="40" height="60" fill="#34495e" rx="8"/>
      <rect x="-18" y="-3" width="36" height="56" fill="#2c3e50" rx="6"/>
      
      <!-- 手臂（指向茶具） -->
      <rect x="20" y="10" width="30" height="8" fill="#f39c12" rx="4" transform="rotate(-10 35 14)"/>
      
      <!-- 智慧光环 -->
      <g opacity="0.4">
        <circle cx="0" cy="-30" r="40" fill="none" stroke="#f1c40f" stroke-width="3"/>
        <circle cx="0" cy="-30" r="50" fill="none" stroke="#f39c12" stroke-width="2"/>
      </g>
    </g>
    
    <!-- 徒弟 -->
    <g transform="translate(150, -20)">
      <!-- 头部 -->
      <circle cx="0" cy="-25" r="20" fill="#3498db"/>
      <circle cx="0" cy="-25" r="15" fill="#2980b9"/>
      
      <!-- 身体 -->
      <rect x="-15" y="-5" width="30" height="50" fill="#3498db" rx="6"/>
      <rect x="-13" y="-3" width="26" height="46" fill="#2980b9" rx="5"/>
      
      <!-- 专注的姿态 -->
      <rect x="-30" y="5" width="15" height="6" fill="#3498db" rx="3" transform="rotate(20 -22 8)"/>
      
      <!-- 思考泡泡 -->
      <g opacity="0.6">
        <circle cx="30" cy="-50" r="15" fill="#ecf0f1"/>
        <circle cx="25" cy="-35" r="8" fill="#ecf0f1"/>
        <circle cx="20" cy="-25" r="5" fill="#ecf0f1"/>
        <text x="30" y="-45" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="12" fill="#2c3e50">?</text>
      </g>
    </g>
    
    <!-- 茶具组合 -->
    <g transform="translate(0, 50)">
      <!-- 茶壶 -->
      <g transform="translate(-80, 0)">
        <ellipse cx="0" cy="20" rx="40" ry="15" fill="#8e44ad"/>
        <rect x="-35" y="5" width="70" height="30" fill="#9b59b6" rx="8"/>
        <ellipse cx="0" cy="5" rx="35" ry="12" fill="#8e44ad"/>
        
        <!-- 壶嘴 -->
        <path d="M 35 15 Q 55 10 60 20 Q 55 25 50 20" fill="#8e44ad"/>
        
        <!-- 壶把 -->
        <path d="M -35 10 Q -50 5 -50 25 Q -50 35 -35 30" stroke="#8e44ad" stroke-width="6" fill="none"/>
        
        <!-- 壶盖 -->
        <ellipse cx="0" cy="0" rx="25" ry="8" fill="#7b68ee"/>
        <circle cx="0" cy="-5" r="4" fill="#6a5acd"/>
      </g>
      
      <!-- 茶杯 -->
      <g transform="translate(80, 0)">
        <ellipse cx="0" cy="25" rx="25" ry="10" fill="#27ae60"/>
        <rect x="-20" y="15" width="40" height="20" fill="#2ecc71" rx="5"/>
        <ellipse cx="0" cy="15" rx="20" ry="8" fill="#27ae60"/>
        
        <!-- 杯把 -->
        <path d="M 20 20 Q 30 15 30 25 Q 30 30 20 28" stroke="#27ae60" stroke-width="4" fill="none"/>
        
        <!-- 茶水 -->
        <ellipse cx="0" cy="18" rx="18" ry="6" fill="#8e44ad" opacity="0.6"/>
      </g>
      
      <!-- 蒸汽 -->
      <g opacity="0.5">
        <path d="M -80 -10 Q -85 -20 -80 -30 Q -75 -40 -80 -50" stroke="#bdc3c7" stroke-width="3" fill="none"/>
        <path d="M -70 -5 Q -75 -15 -70 -25 Q -65 -35 -70 -45" stroke="#bdc3c7" stroke-width="3" fill="none"/>
        <path d="M 80 -5 Q 75 -15 80 -25 Q 85 -35 80 -45" stroke="#bdc3c7" stroke-width="3" fill="none"/>
      </g>
    </g>
  </g>
  
  <!-- 故事框架 -->
  <g transform="translate(960, 650)">
    <!-- 框架背景 -->
    <rect x="-300" y="-40" width="600" height="80" fill="#f4f3ff" rx="20"/>
    <rect x="-280" y="-20" width="560" height="40" fill="#e8e5ff" rx="15"/>
    
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" font-weight="bold" fill="#8e44ad">
      "这就像..."、"你可以把它想象成..."
    </text>
  </g>
  
  <!-- 核心价值 -->
  <g transform="translate(960, 800)">
    <rect x="-450" y="-30" width="900" height="60" fill="#8e44ad" rx="30"/>
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" font-weight="bold" fill="#ffffff">
      把复杂的概念，翻译成用户脑子里已有的"常识"。
    </text>
  </g>
  
  <!-- 装饰性隐喻元素 -->
  <g opacity="0.2">
    <!-- 左侧钥匙 -->
    <g transform="translate(200, 400)">
      <circle cx="0" cy="0" r="15" fill="#f1c40f"/>
      <circle cx="0" cy="0" r="10" fill="#f39c12"/>
      <circle cx="0" cy="0" r="5" fill="#e67e22"/>
      
      <rect x="15" y="-3" width="30" height="6" fill="#f1c40f" rx="3"/>
      <rect x="45" y="3" width="8" height="6" fill="#f1c40f"/>
      <rect x="45" y="9" width="6" height="4" fill="#f1c40f"/>
    </g>
    
    <!-- 右侧锁 -->
    <g transform="translate(1720, 500)">
      <rect x="-15" y="-20" width="30" height="40" fill="#34495e" rx="5"/>
      <rect x="-12" y="-17" width="24" height="34" fill="#2c3e50" rx="3"/>
      
      <circle cx="0" cy="-5" r="8" fill="#7f8c8d"/>
      <rect x="-2" y="-5" width="4" height="15" fill="#7f8c8d"/>
    </g>
  </g>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.3">
    <path d="M 100 950 Q 960 900 1820 950" stroke="#8e44ad" stroke-width="4" fill="none"/>
  </g>
</svg>
