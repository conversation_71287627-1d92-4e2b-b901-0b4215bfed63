<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <g transform="translate(960, 120)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
      标准三：一脉相承 (逻辑顺畅)
    </text>
  </g>
  
  <!-- 路径图 -->
  <g transform="translate(960, 350)">
    <circle cx="0" cy="0" r="100" fill="#f39c12" opacity="0.2"/>
    
    <!-- 清晰的路径 -->
    <g transform="translate(0, -20)">
      <!-- A点到B点的路径 -->
      <circle cx="-150" cy="0" r="25" fill="#e74c3c"/>
      <text x="-150" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="20" font-weight="bold" fill="#ffffff">
        A
      </text>
      
      <circle cx="150" cy="0" r="25" fill="#27ae60"/>
      <text x="150" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="20" font-weight="bold" fill="#ffffff">
        B
      </text>
      
      <!-- 中间步骤 -->
      <circle cx="-75" cy="-30" r="15" fill="#3498db"/>
      <text x="-75" y="-5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="12" fill="#ffffff">
        1
      </text>
      
      <circle cx="0" cy="30" r="15" fill="#9b59b6"/>
      <text x="0" y="35" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="12" fill="#ffffff">
        2
      </text>
      
      <circle cx="75" cy="-20" r="15" fill="#f39c12"/>
      <text x="75" y="-15" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="12" fill="#ffffff">
        3
      </text>
      
      <!-- 连接路径 -->
      <path d="M -125 0 Q -100 -15 -75 -30 Q -37 15 0 30 Q 37 5 75 -20 Q 112 -10 125 0" 
            stroke="#7f8c8d" stroke-width="4" fill="none" marker-end="url(#arrowhead)"/>
      
      <!-- 脚印装饰 -->
      <g opacity="0.4">
        <ellipse cx="-110" cy="10" rx="8" ry="4" fill="#34495e" transform="rotate(-15 -110 10)"/>
        <ellipse cx="-50" cy="-10" rx="8" ry="4" fill="#34495e" transform="rotate(10 -50 -10)"/>
        <ellipse cx="20" cy="20" rx="8" ry="4" fill="#34495e" transform="rotate(-20 20 20)"/>
        <ellipse cx="90" cy="-5" rx="8" ry="4" fill="#34495e" transform="rotate(25 90 -5)"/>
      </g>
    </g>
  </g>
  
  <!-- 描述文字 -->
  <g transform="translate(960, 550)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="32" fill="#2c3e50">
      你的框架，在讲述一个"英雄成长"的完整故事。
    </text>
  </g>
  
  <!-- 三个关键词 -->
  <g transform="translate(960, 700)">
    <!-- 层层递进 -->
    <g transform="translate(-250, 0)">
      <rect x="-80" y="-30" width="160" height="60" fill="#e74c3c" rx="30"/>
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" font-weight="bold" fill="#ffffff">
        层层递进
      </text>
      
      <!-- 递进箭头 -->
      <g transform="translate(0, 80)" opacity="0.6">
        <rect x="-30" y="-5" width="20" height="10" fill="#e74c3c" rx="2"/>
        <rect x="-5" y="-5" width="20" height="10" fill="#e74c3c" rx="2"/>
        <rect x="20" y="-5" width="20" height="10" fill="#e74c3c" rx="2"/>
        <path d="M 45 0 L 55 0" stroke="#e74c3c" stroke-width="3" marker-end="url(#arrowhead1)"/>
      </g>
    </g>
    
    <!-- 环环相扣 -->
    <g transform="translate(0, 0)">
      <rect x="-80" y="-30" width="160" height="60" fill="#3498db" rx="30"/>
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" font-weight="bold" fill="#ffffff">
        环环相扣
      </text>
      
      <!-- 链条 -->
      <g transform="translate(0, 80)" opacity="0.6">
        <ellipse cx="-20" cy="0" rx="12" ry="8" fill="none" stroke="#3498db" stroke-width="3"/>
        <ellipse cx="0" cy="0" rx="12" ry="8" fill="none" stroke="#3498db" stroke-width="3"/>
        <ellipse cx="20" cy="0" rx="12" ry="8" fill="none" stroke="#3498db" stroke-width="3"/>
      </g>
    </g>
    
    <!-- 闭环循环 -->
    <g transform="translate(250, 0)">
      <rect x="-80" y="-30" width="160" height="60" fill="#27ae60" rx="30"/>
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" font-weight="bold" fill="#ffffff">
        闭环循环
      </text>
      
      <!-- 循环箭头 -->
      <g transform="translate(0, 80)" opacity="0.6">
        <circle cx="0" cy="0" r="20" fill="none" stroke="#27ae60" stroke-width="3"/>
        <path d="M 15 -13 L 20 -8 L 15 -3" stroke="#27ae60" stroke-width="3" fill="none"/>
      </g>
    </g>
  </g>
  
  <!-- 装饰性故事元素 -->
  <g opacity="0.2">
    <!-- 左侧书本 -->
    <g transform="translate(200, 400)">
      <rect x="-25" y="-35" width="50" height="70" fill="#8e44ad" rx="5"/>
      <rect x="-20" y="-30" width="40" height="60" fill="#9b59b6" rx="3"/>
      <line x1="-15" y1="-15" x2="15" y2="-15" stroke="#ffffff" stroke-width="2"/>
      <line x1="-15" y1="-5" x2="15" y2="-5" stroke="#ffffff" stroke-width="2"/>
      <line x1="-15" y1="5" x2="15" y2="5" stroke="#ffffff" stroke-width="2"/>
      <line x1="-15" y1="15" x2="15" y2="15" stroke="#ffffff" stroke-width="2"/>
    </g>
    
    <!-- 右侧电影胶片 -->
    <g transform="translate(1720, 500)">
      <rect x="-30" y="-40" width="60" height="80" fill="#34495e" rx="5"/>
      <rect x="-25" y="-35" width="50" height="70" fill="#2c3e50" rx="3"/>
      
      <!-- 胶片孔 -->
      <circle cx="-15" cy="-25" r="3" fill="#ffffff"/>
      <circle cx="15" cy="-25" r="3" fill="#ffffff"/>
      <circle cx="-15" cy="-5" r="3" fill="#ffffff"/>
      <circle cx="15" cy="-5" r="3" fill="#ffffff"/>
      <circle cx="-15" cy="15" r="3" fill="#ffffff"/>
      <circle cx="15" cy="15" r="3" fill="#ffffff"/>
      <circle cx="-15" cy="25" r="3" fill="#ffffff"/>
      <circle cx="15" cy="25" r="3" fill="#ffffff"/>
    </g>
  </g>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#7f8c8d"/>
    </marker>
    <marker id="arrowhead1" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c"/>
    </marker>
  </defs>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.3">
    <path d="M 100 950 Q 960 900 1820 950" stroke="#f39c12" stroke-width="4" fill="none"/>
  </g>
</svg>
