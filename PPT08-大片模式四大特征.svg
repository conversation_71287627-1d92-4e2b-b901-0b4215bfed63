<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <g transform="translate(960, 120)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
      "大片"模式的四大"特征"
    </text>
  </g>
  
  <!-- 四个特征，2x2布局 -->
  <!-- 特征1：只为解决问题 -->
  <g transform="translate(480, 350)">
    <circle cx="0" cy="0" r="120" fill="#27ae60" opacity="0.1"/>
    <circle cx="0" cy="0" r="100" fill="#2ecc71" opacity="0.1"/>
    
    <!-- 靶心被箭射中的图标 -->
    <g transform="translate(0, -30)">
      <circle cx="0" cy="0" r="40" fill="#e74c3c"/>
      <circle cx="0" cy="0" r="30" fill="#ffffff"/>
      <circle cx="0" cy="0" r="20" fill="#e74c3c"/>
      <circle cx="0" cy="0" r="10" fill="#ffffff"/>
      <circle cx="0" cy="0" r="5" fill="#e74c3c"/>
      <!-- 箭 -->
      <rect x="-2" y="-50" width="4" height="40" fill="#8e44ad"/>
      <path d="M 0 -50 L -8 -42 L 8 -42 Z" fill="#8e44ad"/>
      <path d="M -3 -15 L 0 -10 L 3 -15 M -3 -20 L 0 -15 L 3 -20" stroke="#f39c12" stroke-width="1"/>
    </g>
    
    <text x="0" y="80" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" font-weight="bold" fill="#27ae60">
      只为解决问题
    </text>
    <text x="0" y="110" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="20" fill="#7f8c8d">
      危机解决方案
    </text>
  </g>
  
  <!-- 特征2：精心讲好故事 -->
  <g transform="translate(1440, 350)">
    <circle cx="0" cy="0" r="120" fill="#e67e22" opacity="0.1"/>
    <circle cx="0" cy="0" r="100" fill="#f39c12" opacity="0.1"/>
    
    <!-- 篝火讲故事的图标 -->
    <g transform="translate(0, -30)">
      <!-- 篝火 -->
      <ellipse cx="0" cy="20" rx="25" ry="8" fill="#8e44ad"/>
      <path d="M -20 20 Q -10 0 0 20 Q 10 0 20 20" fill="#e74c3c"/>
      <path d="M -15 15 Q -5 -5 5 15 Q 15 -5 15 15" fill="#f39c12"/>
      <path d="M -10 10 Q 0 -10 10 10" fill="#f1c40f"/>
      <!-- 人影 -->
      <circle cx="-35" cy="0" r="8" fill="#34495e"/>
      <rect x="-38" y="8" width="6" height="15" fill="#34495e"/>
      <circle cx="35" cy="0" r="8" fill="#34495e"/>
      <rect x="32" y="8" width="6" height="15" fill="#34495e"/>
    </g>
    
    <text x="0" y="80" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" font-weight="bold" fill="#e67e22">
      精心讲好故事
    </text>
    <text x="0" y="110" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="20" fill="#7f8c8d">
      有血有肉的案例
    </text>
  </g>
  
  <!-- 特征3：讲他不知道的 -->
  <g transform="translate(480, 650)">
    <circle cx="0" cy="0" r="120" fill="#3498db" opacity="0.1"/>
    <circle cx="0" cy="0" r="100" fill="#2980b9" opacity="0.1"/>
    
    <!-- 灯泡在旧脑袋里亮起的图标 -->
    <g transform="translate(0, -30)">
      <ellipse cx="0" cy="0" rx="35" ry="30" fill="#95a5a6"/>
      <ellipse cx="0" cy="-3" rx="30" ry="25" fill="#bdc3c7"/>
      <!-- 灯泡 -->
      <circle cx="0" cy="-5" r="15" fill="#f1c40f"/>
      <rect x="-3" y="8" width="6" height="8" fill="#34495e" rx="1"/>
      <!-- 光芒 -->
      <path d="M 0 -25 L 0 -35 M -18 -15 L -25 -20 M 18 -15 L 25 -20 M -15 5 L -20 10 M 15 5 L 20 10" stroke="#f39c12" stroke-width="2"/>
    </g>
    
    <text x="0" y="80" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" font-weight="bold" fill="#3498db">
      讲他不知道的
    </text>
    <text x="0" y="110" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="20" fill="#7f8c8d">
      颠覆性底层逻辑
    </text>
  </g>
  
  <!-- 特征4：只讲透一招 -->
  <g transform="translate(1440, 650)">
    <circle cx="0" cy="0" r="120" fill="#9b59b6" opacity="0.1"/>
    <circle cx="0" cy="0" r="100" fill="#8e44ad" opacity="0.1"/>
    
    <!-- 锋利手术刀的图标 -->
    <g transform="translate(0, -30)">
      <rect x="-3" y="-40" width="6" height="60" fill="#34495e" rx="1"/>
      <path d="M 0 -40 L -15 -50 L 15 -50 Z" fill="#ecf0f1"/>
      <path d="M 0 -40 L -12 -47 L 12 -47 Z" fill="#bdc3c7"/>
      <rect x="-8" y="20" width="16" height="15" fill="#e74c3c" rx="2"/>
      <!-- 锋利边缘光效 -->
      <path d="M -10 -47 L 10 -47" stroke="#ffffff" stroke-width="1" opacity="0.8"/>
    </g>
    
    <text x="0" y="80" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" font-weight="bold" fill="#9b59b6">
      只讲透一招
    </text>
    <text x="0" y="110" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="20" fill="#7f8c8d">
      锥子式穿透
    </text>
  </g>
  
  <!-- 底部结论 -->
  <g transform="translate(960, 950)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="36" font-weight="bold" fill="#27ae60">
      最终导向："英雄之旅"
    </text>
    <text x="0" y="40" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" fill="#7f8c8d">
      学员是英雄，你是引导他踏上征程的导师
    </text>
  </g>
  
  <!-- 装饰性连接线 -->
  <g opacity="0.3">
    <path d="M 480 470 Q 960 500 1440 470" stroke="#27ae60" stroke-width="3" fill="none"/>
    <path d="M 480 770 Q 960 800 1440 770" stroke="#27ae60" stroke-width="3" fill="none"/>
    <path d="M 600 350 Q 960 400 1320 350" stroke="#27ae60" stroke-width="2" fill="none"/>
    <path d="M 600 650 Q 960 700 1320 650" stroke="#27ae60" stroke-width="2" fill="none"/>
  </g>
  
  <!-- 英雄之旅装饰元素 -->
  <g opacity="0.2">
    <path d="M 200 200 Q 400 150 600 200 Q 800 250 1000 200 Q 1200 150 1400 200 Q 1600 250 1720 200" 
          stroke="#f39c12" stroke-width="4" fill="none" stroke-dasharray="10,5"/>
  </g>
</svg>
