<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 深色背景 -->
  <rect width="1920" height="1080" fill="#2c3e50"/>
  
  <!-- 背景纹理 -->
  <defs>
    <pattern id="texture" x="0" y="0" width="100" height="100" patternUnits="userSpaceOnUse">
      <rect width="100" height="100" fill="#2c3e50"/>
      <circle cx="20" cy="20" r="1" fill="#34495e" opacity="0.5"/>
      <circle cx="60" cy="40" r="1" fill="#34495e" opacity="0.3"/>
      <circle cx="80" cy="80" r="1" fill="#34495e" opacity="0.4"/>
    </pattern>
  </defs>
  <rect width="1920" height="1080" fill="url(#texture)"/>
  
  <!-- 中央巨大问号 -->
  <g transform="translate(960, 400)">
    <!-- 问号阴影 -->
    <g transform="translate(10, 10)" opacity="0.3">
      <path d="M -80 -150 Q -80 -200 -30 -200 Q 80 -200 80 -100 Q 80 -50 30 -20 L 30 50 M 30 80 L 30 120" 
            stroke="#1a252f" stroke-width="40" fill="none" stroke-linecap="round"/>
    </g>
    
    <!-- 主问号 -->
    <path d="M -80 -150 Q -80 -200 -30 -200 Q 80 -200 80 -100 Q 80 -50 30 -20 L 30 50 M 30 80 L 30 120" 
          stroke="#ffffff" stroke-width="35" fill="none" stroke-linecap="round"/>
    
    <!-- 问号装饰光晕 -->
    <circle cx="0" cy="0" r="250" fill="none" stroke="#ffffff" stroke-width="2" opacity="0.1"/>
    <circle cx="0" cy="0" r="300" fill="none" stroke="#ffffff" stroke-width="1" opacity="0.05"/>
  </g>
  
  <!-- 引导性文字 -->
  <g transform="translate(960, 650)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="48" font-weight="bold" fill="#ffffff">
      在"做课"这件事上，什么最让你痛苦？
    </text>
  </g>
  
  <!-- 操作指引 -->
  <g transform="translate(960, 850)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="32" fill="#ecf0f1">
      请用3张便利贴，匿名写下你的答案。
    </text>
    <text x="0" y="50" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="36" font-weight="bold" fill="#e74c3c">
      （3:00）
    </text>
  </g>
  
  <!-- 装饰性便利贴图标 -->
  <g opacity="0.2">
    <!-- 左侧便利贴 -->
    <g transform="translate(200, 200) rotate(-15)">
      <rect x="0" y="0" width="120" height="120" fill="#f39c12" rx="5"/>
      <rect x="10" y="10" width="100" height="100" fill="#f1c40f" rx="3"/>
    </g>
    
    <!-- 右侧便利贴 -->
    <g transform="translate(1600, 300) rotate(20)">
      <rect x="0" y="0" width="120" height="120" fill="#e74c3c" rx="5"/>
      <rect x="10" y="10" width="100" height="100" fill="#e67e22" rx="3"/>
    </g>
    
    <!-- 底部便利贴 -->
    <g transform="translate(400, 800) rotate(-8)">
      <rect x="0" y="0" width="120" height="120" fill="#3498db" rx="5"/>
      <rect x="10" y="10" width="100" height="100" fill="#2980b9" rx="3"/>
    </g>
  </g>
  
  <!-- 边框装饰 -->
  <rect x="20" y="20" width="1880" height="1040" fill="none" stroke="#ffffff" stroke-width="2" opacity="0.1" rx="10"/>
</svg>
