<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <g transform="translate(960, 120)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
      三大经典框架结构"脚手架"
    </text>
  </g>
  
  <!-- 区域一：线性流程型 -->
  <g transform="translate(320, 400)">
    <!-- 背景区域 -->
    <rect x="-250" y="-200" width="500" height="400" fill="#e8f6f3" rx="20"/>
    <rect x="-230" y="-180" width="460" height="360" fill="#d5f4e6" opacity="0.5" rx="15"/>
    
    <!-- 线性流程图标 -->
    <g transform="translate(0, -100)">
      <!-- A→B→C流程 -->
      <rect x="-120" y="-25" width="60" height="50" fill="#27ae60" rx="8"/>
      <text x="-90" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" font-weight="bold" fill="#ffffff">
        A
      </text>
      
      <path d="M -50 0 L -10 0" stroke="#27ae60" stroke-width="4" marker-end="url(#arrowhead1)"/>
      
      <rect x="-30" y="-25" width="60" height="50" fill="#27ae60" rx="8"/>
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" font-weight="bold" fill="#ffffff">
        B
      </text>
      
      <path d="M 40 0 L 80 0" stroke="#27ae60" stroke-width="4" marker-end="url(#arrowhead1)"/>
      
      <rect x="90" y="-25" width="60" height="50" fill="#27ae60" rx="8"/>
      <text x="120" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" font-weight="bold" fill="#ffffff">
        C
      </text>
    </g>
    
    <!-- 标题 -->
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="32" font-weight="bold" fill="#27ae60">
      线性流程型 (A→B→C)
    </text>
    
    <!-- 适用场景 -->
    <text x="0" y="40" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="22" fill="#2c3e50">
      适用：从0到1完成一个项目
    </text>
    
    <!-- 案例 -->
    <g transform="translate(0, 100)">
      <rect x="-100" y="-20" width="200" height="40" fill="#27ae60" rx="20"/>
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" font-weight="bold" fill="#ffffff">
        案例：AIDA营销模型
      </text>
    </g>
  </g>
  
  <!-- 区域二：循环提升型 -->
  <g transform="translate(960, 400)">
    <!-- 背景区域 -->
    <rect x="-250" y="-200" width="500" height="400" fill="#fef5e7" rx="20"/>
    <rect x="-230" y="-180" width="460" height="360" fill="#fdeaa7" opacity="0.5" rx="15"/>
    
    <!-- PDCA循环图标 -->
    <g transform="translate(0, -100)">
      <!-- 循环箭头 -->
      <circle cx="0" cy="0" r="60" fill="none" stroke="#f39c12" stroke-width="6"/>
      
      <!-- P -->
      <g transform="translate(0, -60)">
        <circle cx="0" cy="0" r="20" fill="#f39c12"/>
        <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="16" font-weight="bold" fill="#ffffff">
          P
        </text>
      </g>
      
      <!-- D -->
      <g transform="translate(60, 0)">
        <circle cx="0" cy="0" r="20" fill="#e67e22"/>
        <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="16" font-weight="bold" fill="#ffffff">
          D
        </text>
      </g>
      
      <!-- C -->
      <g transform="translate(0, 60)">
        <circle cx="0" cy="0" r="20" fill="#d35400"/>
        <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="16" font-weight="bold" fill="#ffffff">
          C
        </text>
      </g>
      
      <!-- A -->
      <g transform="translate(-60, 0)">
        <circle cx="0" cy="0" r="20" fill="#f1c40f"/>
        <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="16" font-weight="bold" fill="#ffffff">
          A
        </text>
      </g>
      
      <!-- 循环箭头 -->
      <path d="M 45 -42 L 55 -35 L 45 -28" stroke="#f39c12" stroke-width="3" fill="none"/>
      <path d="M 42 45 L 35 55 L 28 45" stroke="#f39c12" stroke-width="3" fill="none"/>
      <path d="M -45 42 L -55 35 L -45 28" stroke="#f39c12" stroke-width="3" fill="none"/>
      <path d="M -42 -45 L -35 -55 L -28 -45" stroke="#f39c12" stroke-width="3" fill="none"/>
    </g>
    
    <!-- 标题 -->
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="32" font-weight="bold" fill="#f39c12">
      循环提升型 (PDCA)
    </text>
    
    <!-- 适用场景 -->
    <text x="0" y="40" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="22" fill="#2c3e50">
      适用：持续练习、迭代的软技能
    </text>
    
    <!-- 案例 -->
    <g transform="translate(0, 100)">
      <rect x="-100" y="-20" width="200" height="40" fill="#f39c12" rx="20"/>
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" font-weight="bold" fill="#ffffff">
        案例：PDCA戴明环
      </text>
    </g>
  </g>
  
  <!-- 区域三：多维矩阵型 -->
  <g transform="translate(1600, 400)">
    <!-- 背景区域 -->
    <rect x="-250" y="-200" width="500" height="400" fill="#ebf3fd" rx="20"/>
    <rect x="-230" y="-180" width="460" height="360" fill="#c8e6c9" opacity="0.3" rx="15"/>
    
    <!-- 四象限图标 -->
    <g transform="translate(0, -100)">
      <!-- 坐标轴 -->
      <line x1="-60" y1="0" x2="60" y2="0" stroke="#3498db" stroke-width="4"/>
      <line x1="0" y1="-60" x2="0" y2="60" stroke="#3498db" stroke-width="4"/>
      
      <!-- 四个象限 -->
      <rect x="-55" y="-55" width="50" height="50" fill="#e74c3c" opacity="0.7" rx="5"/>
      <text x="-30" y="-25" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="12" fill="#ffffff">
        重要紧急
      </text>
      
      <rect x="5" y="-55" width="50" height="50" fill="#f39c12" opacity="0.7" rx="5"/>
      <text x="30" y="-25" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="12" fill="#ffffff">
        重要不紧急
      </text>
      
      <rect x="-55" y="5" width="50" height="50" fill="#9b59b6" opacity="0.7" rx="5"/>
      <text x="-30" y="35" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="12" fill="#ffffff">
        不重要紧急
      </text>
      
      <rect x="5" y="5" width="50" height="50" fill="#95a5a6" opacity="0.7" rx="5"/>
      <text x="30" y="35" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="12" fill="#ffffff">
        不重要不紧急
      </text>
      
      <!-- 轴标签 -->
      <text x="0" y="-75" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#3498db">
        重要
      </text>
      <text x="75" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#3498db">
        紧急
      </text>
    </g>
    
    <!-- 标题 -->
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="32" font-weight="bold" fill="#3498db">
      多维矩阵型 (四象限)
    </text>
    
    <!-- 适用场景 -->
    <text x="0" y="40" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="22" fill="#2c3e50">
      适用：分析复杂情况、做决策
    </text>
    
    <!-- 案例 -->
    <g transform="translate(0, 100)">
      <rect x="-120" y="-20" width="240" height="40" fill="#3498db" rx="20"/>
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" font-weight="bold" fill="#ffffff">
        案例：重要紧急四象限
      </text>
    </g>
  </g>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="arrowhead1" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#27ae60"/>
    </marker>
  </defs>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.3">
    <path d="M 100 950 Q 960 900 1820 950" stroke="#7f8c8d" stroke-width="4" fill="none"/>
  </g>
</svg>
