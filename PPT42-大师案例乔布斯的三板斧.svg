<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <g transform="translate(960, 120)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
      大师案例：史蒂夫·乔布斯的"三板斧"
    </text>
  </g>
  
  <!-- 乔布斯剪影 -->
  <g transform="translate(960, 400)">
    <!-- 舞台背景 -->
    <rect x="-400" y="-100" width="800" height="300" fill="#2c3e50" opacity="0.1" rx="20"/>
    
    <!-- 乔布斯经典姿态剪影 -->
    <g transform="translate(0, 0)" opacity="0.8">
      <!-- 头部 -->
      <circle cx="0" cy="-80" r="25" fill="#2c3e50"/>
      
      <!-- 身体 -->
      <rect x="-20" y="-55" width="40" height="80" fill="#2c3e50" rx="8"/>
      
      <!-- 手臂（经典手势） -->
      <rect x="-45" y="-40" width="25" height="8" fill="#2c3e50" rx="4" transform="rotate(-20 -32 -36)"/>
      <rect x="20" y="-40" width="25" height="8" fill="#2c3e50" rx="4" transform="rotate(20 32 -36)"/>
      
      <!-- 腿部 -->
      <rect x="-25" y="25" width="15" height="50" fill="#2c3e50"/>
      <rect x="10" y="25" width="15" height="50" fill="#2c3e50"/>
      
      <!-- iPhone在手中 -->
      <rect x="-5" y="-50" width="10" height="20" fill="#34495e" rx="2"/>
      <rect x="-4" y="-49" width="8" height="18" fill="#ecf0f1" rx="1"/>
    </g>
    
    <!-- 聚光灯效果 -->
    <defs>
      <radialGradient id="spotlight2" cx="50%" cy="0%" r="50%">
        <stop offset="0%" style="stop-color:#f1c40f;stop-opacity:0.6"/>
        <stop offset="50%" style="stop-color:#f39c12;stop-opacity:0.3"/>
        <stop offset="100%" style="stop-color:#e67e22;stop-opacity:0.1"/>
      </radialGradient>
    </defs>
    
    <ellipse cx="0" cy="50" rx="200" ry="250" fill="url(#spotlight2)"/>
  </g>
  
  <!-- 视频窗口 -->
  <g transform="translate(960, 650)">
    <!-- 视频框 -->
    <rect x="-300" y="-80" width="600" height="160" fill="#2c3e50" rx="15"/>
    <rect x="-290" y="-70" width="580" height="140" fill="#34495e" rx="10"/>
    
    <!-- 播放按钮 -->
    <g transform="translate(0, 0)">
      <circle cx="0" cy="0" r="40" fill="#e74c3c" opacity="0.8"/>
      <path d="M -15 -20 L -15 20 L 20 0 Z" fill="#ffffff"/>
    </g>
    
    <!-- 视频标题 -->
    <text x="0" y="-100" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" fill="#2c3e50">
      2007年iPhone发布会开场片段
    </text>
    
    <!-- 时长显示 -->
    <text x="250" y="60" text-anchor="end" font-family="Microsoft YaHei, sans-serif" font-size="16" fill="#ecf0f1">
      2:30
    </text>
  </g>
  
  <!-- 装饰性苹果logo -->
  <g opacity="0.2">
    <!-- 左侧苹果 -->
    <g transform="translate(200, 300)">
      <path d="M 0 40 Q -20 20 -15 0 Q -10 -20 0 -15 Q 10 -20 15 0 Q 20 20 0 40 Q -10 35 0 40" fill="#95a5a6"/>
      <path d="M 5 -15 Q 8 -25 15 -20" stroke="#95a5a6" stroke-width="2" fill="none"/>
    </g>
    
    <!-- 右侧苹果 -->
    <g transform="translate(1720, 600)">
      <path d="M 0 30 Q -15 15 -12 0 Q -8 -15 0 -12 Q 8 -15 12 0 Q 15 15 0 30 Q -8 26 0 30" fill="#95a5a6"/>
      <path d="M 4 -12 Q 6 -19 12 -15" stroke="#95a5a6" stroke-width="2" fill="none"/>
    </g>
  </g>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.3">
    <path d="M 100 950 Q 960 900 1820 950" stroke="#2c3e50" stroke-width="4" fill="none"/>
  </g>
</svg>
