<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <g transform="translate(960, 120)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
      第一剑：卖话术
    </text>
  </g>
  
  <!-- 主视觉：漏斗转化图 -->
  <g transform="translate(960, 400)">
    <!-- 漏斗主体 -->
    <g>
      <!-- 漏斗顶部 -->
      <path d="M -200 -150 L 200 -150 L 100 50 L -100 50 Z" fill="#3498db"/>
      <path d="M -180 -130 L 180 -130 L 90 30 L -90 30 Z" fill="#2980b9"/>
      
      <!-- 漏斗底部 -->
      <rect x="-50" y="50" width="100" height="100" fill="#34495e" rx="5"/>
      <rect x="-40" y="60" width="80" height="80" fill="#2c3e50" rx="3"/>
    </g>
    
    <!-- 漏斗上方的课程内容 -->
    <g transform="translate(-150, -200)">
      <rect x="-60" y="-30" width="120" height="60" fill="#e74c3c" rx="10"/>
      <text x="0" y="-5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="16" font-weight="bold" fill="#ffffff">
        开场
      </text>
      <text x="0" y="15" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="12" fill="#ffffff">
        创造需求
      </text>
    </g>
    
    <g transform="translate(0, -200)">
      <rect x="-60" y="-30" width="120" height="60" fill="#f39c12" rx="10"/>
      <text x="0" y="-5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="16" font-weight="bold" fill="#ffffff">
        中场
      </text>
      <text x="0" y="15" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="12" fill="#ffffff">
        建立价值
      </text>
    </g>
    
    <g transform="translate(150, -200)">
      <rect x="-60" y="-30" width="120" height="60" fill="#27ae60" rx="10"/>
      <text x="0" y="-5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="16" font-weight="bold" fill="#ffffff">
        收场
      </text>
      <text x="0" y="15" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="12" fill="#ffffff">
        描绘未来
      </text>
    </g>
    
    <!-- 漏斗下方的购买按钮 -->
    <g transform="translate(0, 200)">
      <rect x="-80" y="-25" width="160" height="50" fill="#e74c3c" rx="25"/>
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="20" font-weight="bold" fill="#ffffff">
        购买
      </text>
    </g>
    
    <!-- 流动箭头 -->
    <g opacity="0.6">
      <defs>
        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
          <polygon points="0 0, 10 3.5, 0 7" fill="#f39c12"/>
        </marker>
      </defs>
      
      <path d="M -150 -140 L -120 -120" stroke="#f39c12" stroke-width="4" fill="none" marker-end="url(#arrowhead)"/>
      <path d="M 0 -140 L 0 -120" stroke="#f39c12" stroke-width="4" fill="none" marker-end="url(#arrowhead)"/>
      <path d="M 150 -140 L 120 -120" stroke="#f39c12" stroke-width="4" fill="none" marker-end="url(#arrowhead)"/>
      <path d="M 0 150 L 0 175" stroke="#f39c12" stroke-width="4" fill="none" marker-end="url(#arrowhead)"/>
    </g>
  </g>
  
  <!-- 核心理念 -->
  <g transform="translate(960, 650)">
    <rect x="-450" y="-30" width="900" height="60" fill="#f8f9fa" rx="30"/>
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" font-weight="bold" fill="#2c3e50">
      你的整个课程内容，就是最强的销售文案。
    </text>
  </g>
  
  <!-- 拆解说明 -->
  <g transform="translate(960, 750)">
    <rect x="-400" y="-40" width="800" height="80" fill="#e74c3c" rx="20"/>
    
    <text x="-200" y="-10" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="20" font-weight="bold" fill="#ffffff">
      开场 = 创造需求
    </text>
    <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="20" font-weight="bold" fill="#ffffff">
      中场 = 建立价值
    </text>
    <text x="200" y="-10" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="20" font-weight="bold" fill="#ffffff">
      收场 = 描绘未来
    </text>
  </g>
  
  <!-- 装饰性营销元素 -->
  <g opacity="0.2">
    <!-- 左侧文案 -->
    <g transform="translate(200, 500)">
      <rect x="-25" y="-30" width="50" height="60" fill="#3498db" rx="5"/>
      <rect x="-20" y="-25" width="40" height="50" fill="#2980b9" rx="3"/>
      
      <!-- 文案线条 -->
      <line x1="-15" y1="-15" x2="15" y2="-15" stroke="#ffffff" stroke-width="2"/>
      <line x1="-15" y1="-5" x2="15" y2="-5" stroke="#ffffff" stroke-width="2"/>
      <line x1="-15" y1="5" x2="15" y2="5" stroke="#ffffff" stroke-width="2"/>
      <line x1="-15" y1="15" x2="10" y2="15" stroke="#ffffff" stroke-width="2"/>
    </g>
    
    <!-- 右侧转化图标 -->
    <g transform="translate(1720, 450)">
      <circle cx="0" cy="0" r="20" fill="#27ae60"/>
      <path d="M -8 0 L 8 0 M 3 -5 L 8 0 L 3 5" stroke="#ffffff" stroke-width="3" fill="none"/>
      
      <!-- 百分比 -->
      <text x="0" y="40" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#27ae60">
        转化
      </text>
    </g>
  </g>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.3">
    <path d="M 100 950 Q 960 900 1820 950" stroke="#e74c3c" stroke-width="4" fill="none"/>
  </g>
</svg>
