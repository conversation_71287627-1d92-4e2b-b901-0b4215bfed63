<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <g transform="translate(960, 120)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
      第三板斧：塑造挑战
    </text>
  </g>
  
  <!-- 主视觉：钥匙插在锁孔里 -->
  <g transform="translate(960, 400)">
    <!-- 锁的主体 -->
    <g>
      <!-- 锁身 -->
      <rect x="-80" y="-60" width="160" height="120" fill="#34495e" rx="20"/>
      <rect x="-70" y="-50" width="140" height="100" fill="#2c3e50" rx="15"/>
      
      <!-- 锁孔 -->
      <circle cx="0" cy="0" r="25" fill="#7f8c8d"/>
      <circle cx="0" cy="0" r="20" fill="#95a5a6"/>
      <rect x="-3" y="0" width="6" height="30" fill="#95a5a6"/>
      
      <!-- 锁的装饰线条 -->
      <rect x="-60" y="-40" width="120" height="4" fill="#7f8c8d" rx="2"/>
      <rect x="-60" y="36" width="120" height="4" fill="#7f8c8d" rx="2"/>
    </g>
    
    <!-- 钥匙 -->
    <g transform="translate(0, -20)">
      <!-- 钥匙柄 -->
      <circle cx="-100" cy="0" r="25" fill="#f39c12"/>
      <circle cx="-100" cy="0" r="20" fill="#e67e22"/>
      <circle cx="-100" cy="0" r="8" fill="#d35400"/>
      
      <!-- 钥匙杆 -->
      <rect x="-75" y="-4" width="75" height="8" fill="#f39c12" rx="4"/>
      <rect x="-75" y="-3" width="75" height="6" fill="#e67e22" rx="3"/>
      
      <!-- 钥匙齿 -->
      <rect x="0" y="4" width="15" height="8" fill="#f39c12"/>
      <rect x="0" y="12" width="10" height="6" fill="#f39c12"/>
      <rect x="0" y="18" width="12" height="4" fill="#f39c12"/>
    </g>
    
    <!-- 光芒效果 -->
    <g opacity="0.6">
      <circle cx="0" cy="0" r="120" fill="none" stroke="#f1c40f" stroke-width="3" opacity="0.4"/>
      <circle cx="0" cy="0" r="140" fill="none" stroke="#f39c12" stroke-width="2" opacity="0.3"/>
      <circle cx="0" cy="0" r="160" fill="none" stroke="#e67e22" stroke-width="1" opacity="0.2"/>
    </g>
  </g>
  
  <!-- 核心要点 -->
  <g transform="translate(960, 650)">
    <!-- 目标 -->
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" font-weight="bold" fill="#3498db">
      目标：点明改变的真正难度，并塑造你的课程为"唯一的钥匙"。
    </text>
    
    <!-- 技巧框 -->
    <g transform="translate(0, 80)">
      <rect x="-250" y="-40" width="500" height="100" fill="#ebf3fd" rx="15"/>
      <rect x="-230" y="-20" width="460" height="60" fill="#c8e6c9" opacity="0.3" rx="10"/>
      
      <text x="0" y="-30" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" font-weight="bold" fill="#2c3e50">
        技巧：
      </text>
      
      <g transform="translate(-80, 0)">
        <circle cx="0" cy="0" r="4" fill="#2980b9"/>
        <text x="15" y="5" font-family="Microsoft YaHei, sans-serif" font-size="20" fill="#2c3e50">
          点明真因
        </text>
      </g>
      
      <g transform="translate(80, 0)">
        <circle cx="0" cy="0" r="4" fill="#1f4e79"/>
        <text x="15" y="5" font-family="Microsoft YaHei, sans-serif" font-size="20" fill="#2c3e50">
          建立价值
        </text>
      </g>
    </g>
  </g>
  
  <!-- 感受标签 -->
  <g transform="translate(960, 850)">
    <rect x="-200" y="-30" width="400" height="60" fill="#3498db" rx="30"/>
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" font-weight="bold" fill="#ffffff">
      让他相信："只有你能带我去！"
    </text>
  </g>
  
  <!-- 装饰性钥匙元素 -->
  <g opacity="0.2">
    <!-- 左侧小钥匙 -->
    <g transform="translate(200, 300)">
      <circle cx="0" cy="0" r="15" fill="#f39c12"/>
      <rect x="15" y="-2" width="30" height="4" fill="#f39c12" rx="2"/>
      <rect x="45" y="2" width="8" height="4" fill="#f39c12"/>
      <rect x="45" y="6" width="6" height="3" fill="#f39c12"/>
    </g>
    
    <!-- 右侧小钥匙 -->
    <g transform="translate(1720, 600)">
      <circle cx="0" cy="0" r="12" fill="#3498db"/>
      <rect x="12" y="-2" width="25" height="4" fill="#3498db" rx="2"/>
      <rect x="37" y="2" width="6" height="3" fill="#3498db"/>
      <rect x="37" y="5" width="5" height="2" fill="#3498db"/>
    </g>
  </g>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.3">
    <path d="M 100 950 Q 960 900 1820 950" stroke="#3498db" stroke-width="4" fill="none"/>
  </g>
</svg>
