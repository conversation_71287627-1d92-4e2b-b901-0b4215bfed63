<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 电影最后镜头背景 -->
  <g opacity="0.2">
    <!-- 远山轮廓 -->
    <path d="M 0 400 L 200 300 L 400 350 L 600 250 L 800 300 L 1000 200 L 1200 250 L 1400 150 L 1600 200 L 1800 100 L 1920 150 L 1920 1080 L 0 1080 Z" 
          fill="#95a5a6"/>
    <path d="M 0 450 L 300 350 L 600 400 L 900 300 L 1200 350 L 1500 250 L 1800 300 L 1920 200 L 1920 1080 L 0 1080 Z" 
          fill="#bdc3c7"/>
    
    <!-- 夕阳 -->
    <circle cx="1600" cy="200" r="80" fill="#f39c12" opacity="0.6"/>
    <circle cx="1600" cy="200" r="60" fill="#f1c40f" opacity="0.8"/>
    
    <!-- 光线 -->
    <g opacity="0.4">
      <path d="M 1520 120 L 1680 280" stroke="#f1c40f" stroke-width="3"/>
      <path d="M 1540 100 L 1700 260" stroke="#f39c12" stroke-width="2"/>
      <path d="M 1560 90 L 1720 250" stroke="#f1c40f" stroke-width="3"/>
      <path d="M 1580 85 L 1740 245" stroke="#f39c12" stroke-width="2"/>
    </g>
  </g>
  
  <!-- 主角背影 -->
  <g transform="translate(960, 600)" opacity="0.6">
    <!-- 人物剪影 -->
    <g>
      <!-- 头部 -->
      <circle cx="0" cy="-80" r="25" fill="#2c3e50"/>
      
      <!-- 身体 -->
      <rect x="-20" y="-55" width="40" height="80" fill="#2c3e50" rx="8"/>
      
      <!-- 手臂 -->
      <rect x="-45" y="-40" width="25" height="10" fill="#2c3e50" rx="5"/>
      <rect x="20" y="-40" width="25" height="10" fill="#2c3e50" rx="5"/>
      
      <!-- 腿部 -->
      <rect x="-25" y="25" width="18" height="60" fill="#2c3e50"/>
      <rect x="7" y="25" width="18" height="60" fill="#2c3e50"/>
      
      <!-- 斗篷飘扬 -->
      <path d="M -20 -50 Q -40 -30 -35 10 Q -30 50 -20 80 Q -10 90 0 85 Q 10 80 20 85 Q 30 90 25 50 Q 30 10 35 -30 Q 40 -50 20 -50" 
            fill="#34495e" opacity="0.8"/>
    </g>
    
    <!-- 脚步印记 -->
    <g opacity="0.4">
      <ellipse cx="-15" cy="100" rx="8" ry="4" fill="#7f8c8d"/>
      <ellipse cx="15" cy="110" rx="8" ry="4" fill="#7f8c8d"/>
      <ellipse cx="-10" cy="120" rx="8" ry="4" fill="#7f8c8d"/>
      <ellipse cx="20" cy="130" rx="8" ry="4" fill="#7f8c8d"/>
    </g>
  </g>
  
  <!-- 主要文字内容 -->
  <g transform="translate(960, 300)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="64" font-weight="bold" fill="#2c3e50">
      你有了思想的骨架，也有了情感的血肉。
    </text>
  </g>
  
  <!-- 总结和作业 -->
  <g transform="translate(960, 450)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="32" fill="#7f8c8d">
      本日总结：从"思想破局者"到"情感魔法师"。
    </text>
  </g>
  
  <!-- 晚间作业 -->
  <g transform="translate(960, 750)">
    <rect x="-450" y="-80" width="900" height="160" fill="#f8f9fa" rx="20"/>
    <rect x="-430" y="-60" width="860" height="120" fill="#e9ecef" opacity="0.5" rx="15"/>
    
    <text x="0" y="-30" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" font-weight="bold" fill="#2c3e50">
      晚间作业：
    </text>
    
    <text x="0" y="10" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" fill="#7f8c8d">
      1. 把你的核心故事，讲给一个人听。
    </text>
    
    <text x="0" y="40" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" fill="#7f8c8d">
      2. 为你的课程框架，设计3个"悬念钩子"。
    </text>
  </g>
  
  <!-- 装饰性电影元素 -->
  <g opacity="0.3">
    <!-- 左侧胶片 -->
    <g transform="translate(200, 400)">
      <rect x="-15" y="-40" width="30" height="80" fill="#2c3e50" rx="3"/>
      <circle cx="-8" cy="-25" r="3" fill="#ecf0f1"/>
      <circle cx="8" cy="-25" r="3" fill="#ecf0f1"/>
      <circle cx="-8" cy="-5" r="3" fill="#ecf0f1"/>
      <circle cx="8" cy="-5" r="3" fill="#ecf0f1"/>
      <circle cx="-8" cy="15" r="3" fill="#ecf0f1"/>
      <circle cx="8" cy="15" r="3" fill="#ecf0f1"/>
      <circle cx="-8" cy="35" r="3" fill="#ecf0f1"/>
      <circle cx="8" cy="35" r="3" fill="#ecf0f1"/>
    </g>
    
    <!-- 右侧导演椅 -->
    <g transform="translate(1720, 500)">
      <rect x="-20" y="-30" width="40" height="60" fill="#8e44ad" rx="5"/>
      <rect x="-25" y="30" width="50" height="8" fill="#9b59b6" rx="2"/>
      <rect x="-3" y="38" width="6" height="30" fill="#8e44ad"/>
      <rect x="-25" y="68" width="50" height="8" fill="#9b59b6" rx="2"/>
    </g>
  </g>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.4">
    <path d="M 200 950 Q 960 850 1720 950" stroke="#f39c12" stroke-width="8" fill="none"/>
    <path d="M 300 980 Q 960 880 1620 980" stroke="#e67e22" stroke-width="6" fill="none"/>
  </g>
  
  <!-- 底部装饰线条 -->
  <g opacity="0.3">
    <rect x="0" y="1000" width="1920" height="4" fill="#f39c12"/>
    <rect x="0" y="1020" width="1920" height="3" fill="#e67e22"/>
    <rect x="0" y="1035" width="1920" height="2" fill="#d35400"/>
  </g>
</svg>
