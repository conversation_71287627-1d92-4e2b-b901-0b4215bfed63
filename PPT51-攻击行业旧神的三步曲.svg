<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <g transform="translate(960, 150)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
      攻击"行业旧神"的三步曲
    </text>
  </g>
  
  <!-- Step 1：锁定"旧神" -->
  <g transform="translate(320, 400)">
    <!-- 背景区域 -->
    <rect x="-200" y="-150" width="400" height="300" fill="#ffebee" rx="20"/>
    <rect x="-180" y="-130" width="360" height="260" fill="#ffcdd2" opacity="0.5" rx="15"/>
    
    <!-- 瞄准镜图标 -->
    <g transform="translate(0, -50)">
      <circle cx="0" cy="0" r="50" fill="none" stroke="#e74c3c" stroke-width="6"/>
      <circle cx="0" cy="0" r="35" fill="none" stroke="#c0392b" stroke-width="4"/>
      <circle cx="0" cy="0" r="20" fill="none" stroke="#e74c3c" stroke-width="3"/>
      <circle cx="0" cy="0" r="8" fill="#e74c3c"/>
      
      <!-- 十字准星 -->
      <line x1="-60" y1="0" x2="-50" y2="0" stroke="#e74c3c" stroke-width="4"/>
      <line x1="50" y1="0" x2="60" y2="0" stroke="#e74c3c" stroke-width="4"/>
      <line x1="0" y1="-60" x2="0" y2="-50" stroke="#e74c3c" stroke-width="4"/>
      <line x1="0" y1="50" x2="0" y2="60" stroke="#e74c3c" stroke-width="4"/>
    </g>
    
    <!-- 步骤标识 -->
    <g transform="translate(-150, -100)">
      <circle cx="0" cy="0" r="20" fill="#e74c3c"/>
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="16" font-weight="bold" fill="#ffffff">
        1
      </text>
    </g>
    
    <!-- 标题 -->
    <text x="0" y="50" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" font-weight="bold" fill="#e74c3c">
      锁定"旧神"
    </text>
    
    <!-- 描述 -->
    <text x="0" y="80" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" fill="#7f8c8d">
      （找到那个"政治正确"
    </text>
    <text x="0" y="105" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" fill="#7f8c8d">
      但错误的行业常识）
    </text>
  </g>
  
  <!-- Step 2：系统性攻击 -->
  <g transform="translate(960, 400)">
    <!-- 背景区域 -->
    <rect x="-200" y="-150" width="400" height="300" fill="#fef5e7" rx="20"/>
    <rect x="-180" y="-130" width="360" height="260" fill="#fdeaa7" opacity="0.5" rx="15"/>
    
    <!-- 武器组图标 -->
    <g transform="translate(0, -50)">
      <!-- 剑 -->
      <rect x="-30" y="-40" width="6" height="80" fill="#f39c12" rx="1"/>
      <rect x="-35" y="-50" width="16" height="15" fill="#e67e22" rx="3"/>
      
      <!-- 弓箭 -->
      <path d="M 10 -40 Q 30 -20 10 0" stroke="#f39c12" stroke-width="4" fill="none"/>
      <line x1="15" y1="-20" x2="45" y2="-20" stroke="#e67e22" stroke-width="3"/>
      <path d="M 40 -25 L 50 -20 L 40 -15" stroke="#e67e22" stroke-width="2" fill="none"/>
      
      <!-- 盾牌 -->
      <path d="M -10 20 Q -30 25 -30 45 Q -30 65 -10 80 Q 10 65 10 45 Q 10 25 -10 20 Z" fill="#f39c12"/>
      <path d="M -10 25 Q -25 30 -25 45 Q -25 60 -10 75 Q 5 60 5 45 Q 5 30 -10 25 Z" fill="#e67e22"/>
      
      <!-- 锤子 -->
      <rect x="20" y="35" width="4" height="40" fill="#f39c12" rx="1"/>
      <rect x="15" y="25" width="14" height="8" fill="#e67e22" rx="2"/>
    </g>
    
    <!-- 步骤标识 -->
    <g transform="translate(-150, -100)">
      <circle cx="0" cy="0" r="20" fill="#f39c12"/>
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="16" font-weight="bold" fill="#ffffff">
        2
      </text>
    </g>
    
    <!-- 标题 -->
    <text x="0" y="50" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" font-weight="bold" fill="#f39c12">
      系统性攻击
    </text>
    
    <!-- 描述 -->
    <text x="0" y="80" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" fill="#7f8c8d">
      （用多种武器，全面论证
    </text>
    <text x="0" y="105" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" fill="#7f8c8d">
      它为何是错的）
    </text>
  </g>
  
  <!-- Step 3：建立"新神" -->
  <g transform="translate(1600, 400)">
    <!-- 背景区域 -->
    <rect x="-200" y="-150" width="400" height="300" fill="#e8f5e8" rx="20"/>
    <rect x="-180" y="-130" width="360" height="260" fill="#c8e6c9" opacity="0.5" rx="15"/>
    
    <!-- 新神庙图标 -->
    <g transform="translate(0, -30)">
      <!-- 神庙底座 -->
      <rect x="-60" y="40" width="120" height="20" fill="#27ae60" rx="5"/>
      <rect x="-55" y="45" width="110" height="10" fill="#2ecc71" rx="3"/>
      
      <!-- 柱子 -->
      <rect x="-40" y="0" width="12" height="40" fill="#27ae60"/>
      <rect x="-15" y="0" width="12" height="40" fill="#27ae60"/>
      <rect x="10" y="0" width="12" height="40" fill="#27ae60"/>
      <rect x="35" y="0" width="12" height="40" fill="#27ae60"/>
      
      <!-- 屋顶 -->
      <path d="M -50 0 L 0 -40 L 50 0 Z" fill="#2ecc71"/>
      <path d="M -45 -5 L 0 -35 L 45 -5 Z" fill="#27ae60"/>
      
      <!-- 光芒 -->
      <g opacity="0.6">
        <line x1="0" y1="-60" x2="0" y2="-80" stroke="#f1c40f" stroke-width="3"/>
        <line x1="-20" y1="-50" x2="-30" y2="-65" stroke="#f39c12" stroke-width="2"/>
        <line x1="20" y1="-50" x2="30" y2="-65" stroke="#f39c12" stroke-width="2"/>
      </g>
    </g>
    
    <!-- 步骤标识 -->
    <g transform="translate(-150, -100)">
      <circle cx="0" cy="0" r="20" fill="#27ae60"/>
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="16" font-weight="bold" fill="#ffffff">
        3
      </text>
    </g>
    
    <!-- 标题 -->
    <text x="0" y="50" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" font-weight="bold" fill="#27ae60">
      建立"新神"
    </text>
    
    <!-- 描述 -->
    <text x="0" y="80" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" fill="#7f8c8d">
      （在废墟上，建立你的
    </text>
    <text x="0" y="105" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" fill="#7f8c8d">
      思想体系）
    </text>
  </g>
  
  <!-- 连接箭头 -->
  <g opacity="0.4">
    <path d="M 520 400 L 760 400" stroke="#7f8c8d" stroke-width="6" fill="none" marker-end="url(#arrowhead1)"/>
    <path d="M 1160 400 L 1400 400" stroke="#7f8c8d" stroke-width="6" fill="none" marker-end="url(#arrowhead2)"/>
  </g>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="arrowhead1" markerWidth="12" markerHeight="8" refX="10" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#7f8c8d"/>
    </marker>
    <marker id="arrowhead2" markerWidth="12" markerHeight="8" refX="10" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#7f8c8d"/>
    </marker>
  </defs>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.3">
    <path d="M 100 950 Q 960 900 1820 950" stroke="#e74c3c" stroke-width="4" fill="none"/>
  </g>
</svg>
