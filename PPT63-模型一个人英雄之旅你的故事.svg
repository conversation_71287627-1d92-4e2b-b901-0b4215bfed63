<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <g transform="translate(960, 120)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
      模型一：个人英雄之旅 (你的故事)
    </text>
  </g>
  
  <!-- 主视觉：从低谷走向高峰的山路 -->
  <g transform="translate(960, 350)">
    <!-- 山峰轮廓 -->
    <g opacity="0.3">
      <path d="M -400 200 L -300 100 L -200 150 L -100 50 L 0 100 L 100 0 L 200 50 L 300 -50 L 400 0 L 500 -100" 
            stroke="#95a5a6" stroke-width="4" fill="none"/>
      <path d="M -400 200 L -300 100 L -200 150 L -100 50 L 0 100 L 100 0 L 200 50 L 300 -50 L 400 0 L 500 -100 L 500 250 L -400 250 Z" 
            fill="#bdc3c7" opacity="0.2"/>
    </g>
    
    <!-- 英雄之旅路径 -->
    <g>
      <!-- 起点（低谷） -->
      <g transform="translate(-300, 150)">
        <circle cx="0" cy="0" r="15" fill="#e74c3c"/>
        <circle cx="0" cy="0" r="10" fill="#c0392b"/>
        <text x="0" y="40" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="16" fill="#e74c3c">
          起点
        </text>
      </g>
      
      <!-- 转折点 -->
      <g transform="translate(0, 50)">
        <circle cx="0" cy="0" r="15" fill="#f39c12"/>
        <circle cx="0" cy="0" r="10" fill="#e67e22"/>
        <text x="0" y="40" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="16" fill="#f39c12">
          转折
        </text>
      </g>
      
      <!-- 终点（高峰） -->
      <g transform="translate(300, -50)">
        <circle cx="0" cy="0" r="15" fill="#27ae60"/>
        <circle cx="0" cy="0" r="10" fill="#2ecc71"/>
        <text x="0" y="40" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="16" fill="#27ae60">
          终点
        </text>
      </g>
      
      <!-- 连接路径 -->
      <path d="M -285 150 Q -100 100 0 50 Q 150 -20 285 -50" 
            stroke="#e74c3c" stroke-width="6" fill="none" stroke-dasharray="10,5"/>
      
      <!-- 路径箭头 -->
      <defs>
        <marker id="arrowhead" markerWidth="12" markerHeight="8" refX="10" refY="4" orient="auto">
          <polygon points="0 0, 12 4, 0 8" fill="#e74c3c"/>
        </marker>
      </defs>
      <path d="M 250 -35 L 285 -50" stroke="#e74c3c" stroke-width="6" fill="none" marker-end="url(#arrowhead)"/>
    </g>
  </g>
  
  <!-- 故事框架 -->
  <g transform="translate(960, 600)">
    <!-- 框架背景 -->
    <rect x="-450" y="-100" width="900" height="200" fill="#f8f9fa" rx="20"/>
    <rect x="-430" y="-80" width="860" height="160" fill="#e9ecef" opacity="0.5" rx="15"/>
    
    <!-- 三个阶段 -->
    <g transform="translate(-250, -30)">
      <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="20" font-weight="bold" fill="#e74c3c">
        "我曾经也和你们一样，甚至更惨..." (起点)
      </text>
    </g>
    
    <g transform="translate(0, 0)">
      <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="20" font-weight="bold" fill="#f39c12">
        "直到有一天，我悟到了..." (转折)
      </text>
    </g>
    
    <g transform="translate(250, 30)">
      <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="20" font-weight="bold" fill="#27ae60">
        "最终，我..." (终点)
      </text>
    </g>
  </g>
  
  <!-- 核心价值 -->
  <g transform="translate(960, 800)">
    <rect x="-400" y="-30" width="800" height="60" fill="#e74c3c" rx="30"/>
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" font-weight="bold" fill="#ffffff">
      你不是神，你是"从坑里爬出来"的领路人。
    </text>
  </g>
  
  <!-- 装饰性英雄元素 -->
  <g opacity="0.2">
    <!-- 左侧剑 -->
    <g transform="translate(200, 400)">
      <rect x="-3" y="-40" width="6" height="80" fill="#e74c3c" rx="1"/>
      <rect x="-8" y="-50" width="16" height="15" fill="#c0392b" rx="3"/>
      <ellipse cx="0" cy="45" rx="12" ry="8" fill="#8e44ad"/>
    </g>
    
    <!-- 右侧盾牌 -->
    <g transform="translate(1720, 500)">
      <path d="M 0 -30 Q -20 -25 -20 0 Q -20 25 0 40 Q 20 25 20 0 Q 20 -25 0 -30 Z" fill="#27ae60"/>
      <path d="M 0 -25 Q -15 -20 -15 0 Q -15 20 0 35 Q 15 20 15 0 Q 15 -20 0 -25 Z" fill="#2ecc71"/>
    </g>
  </g>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.3">
    <path d="M 100 950 Q 960 900 1820 950" stroke="#e74c3c" stroke-width="4" fill="none"/>
  </g>
</svg>
