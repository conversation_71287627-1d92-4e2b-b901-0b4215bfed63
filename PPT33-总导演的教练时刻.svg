<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 电影台词风格的背景 -->
  <g opacity="0.1">
    <!-- 胶片纹理 -->
    <rect x="0" y="0" width="1920" height="1080" fill="#2c3e50"/>
    <g opacity="0.3">
      <rect x="0" y="100" width="1920" height="20" fill="#34495e"/>
      <rect x="0" y="200" width="1920" height="20" fill="#34495e"/>
      <rect x="0" y="300" width="1920" height="20" fill="#34495e"/>
      <rect x="0" y="400" width="1920" height="20" fill="#34495e"/>
      <rect x="0" y="500" width="1920" height="20" fill="#34495e"/>
      <rect x="0" y="600" width="1920" height="20" fill="#34495e"/>
      <rect x="0" y="700" width="1920" height="20" fill="#34495e"/>
      <rect x="0" y="800" width="1920" height="20" fill="#34495e"/>
      <rect x="0" y="900" width="1920" height="20" fill="#34495e"/>
    </g>
  </g>
  
  <!-- 主要文字内容 -->
  <g transform="translate(960, 400)">
    <!-- 引号装饰 -->
    <text x="-400" y="-100" font-family="Microsoft YaHei, sans-serif" font-size="120" fill="#e74c3c" opacity="0.3">
      "
    </text>
    <text x="400" y="100" font-family="Microsoft YaHei, sans-serif" font-size="120" fill="#e74c3c" opacity="0.3">
      "
    </text>
    
    <!-- 核心文字 -->
    <text x="0" y="-50" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="48" font-weight="bold" fill="#2c3e50">
      我看到你卡住了，没关系。
    </text>
    
    <text x="0" y="20" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="48" font-weight="bold" fill="#2c3e50">
      我们不想那么多模块，就想三步。
    </text>
    
    <text x="0" y="90" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="48" font-weight="bold" fill="#2c3e50">
      如果你的学员要从痛苦的A点到理想的B点，
    </text>
    
    <text x="0" y="160" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="48" font-weight="bold" fill="#e74c3c">
      他必须翻越的三座大山，是什么？
    </text>
  </g>
  
  <!-- 右下角署名 -->
  <g transform="translate(1600, 900)">
    <text x="0" y="0" text-anchor="end" font-family="Microsoft YaHei, sans-serif" font-size="28" fill="#7f8c8d" font-style="italic">
      —— 总导演的"教练时刻"
    </text>
  </g>
  
  <!-- 装饰性山峰图标 -->
  <g opacity="0.2">
    <!-- 左侧山峰 -->
    <g transform="translate(200, 700)">
      <path d="M 0 100 L 40 20 L 80 60 L 120 0 L 160 80 L 200 100 Z" fill="#95a5a6"/>
      <path d="M 0 100 L 35 25 L 70 65 L 110 5 L 150 85 L 200 100 Z" fill="#bdc3c7"/>
    </g>
    
    <!-- 右侧山峰 -->
    <g transform="translate(1520, 650)">
      <path d="M 0 150 L 50 30 L 100 80 L 150 10 L 200 120 L 250 150 Z" fill="#95a5a6"/>
      <path d="M 0 150 L 45 35 L 90 85 L 140 15 L 190 125 L 250 150 Z" fill="#bdc3c7"/>
    </g>
  </g>
  
  <!-- A点到B点的路径示意 -->
  <g transform="translate(960, 750)" opacity="0.3">
    <!-- A点 -->
    <circle cx="-200" cy="0" r="25" fill="#e74c3c"/>
    <text x="-200" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="20" font-weight="bold" fill="#ffffff">
      A
    </text>
    <text x="-200" y="50" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="16" fill="#e74c3c">
      痛苦现状
    </text>
    
    <!-- B点 -->
    <circle cx="200" cy="0" r="25" fill="#27ae60"/>
    <text x="200" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="20" font-weight="bold" fill="#ffffff">
      B
    </text>
    <text x="200" y="50" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="16" fill="#27ae60">
      理想目标
    </text>
    
    <!-- 三座大山 -->
    <g transform="translate(-100, -50)">
      <path d="M 0 50 L 20 10 L 40 50 Z" fill="#7f8c8d"/>
      <text x="20" y="70" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="12" fill="#7f8c8d">
        山1
      </text>
    </g>
    
    <g transform="translate(0, -60)">
      <path d="M 0 60 L 25 5 L 50 60 Z" fill="#7f8c8d"/>
      <text x="25" y="80" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="12" fill="#7f8c8d">
        山2
      </text>
    </g>
    
    <g transform="translate(100, -45)">
      <path d="M 0 45 L 18 15 L 36 45 Z" fill="#7f8c8d"/>
      <text x="18" y="65" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="12" fill="#7f8c8d">
        山3
      </text>
    </g>
    
    <!-- 路径线 -->
    <path d="M -175 0 Q -50 -30 0 -40 Q 50 -35 175 0" stroke="#f39c12" stroke-width="3" fill="none" stroke-dasharray="5,5"/>
  </g>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.3">
    <path d="M 100 1000 Q 960 950 1820 1000" stroke="#e74c3c" stroke-width="4" fill="none"/>
  </g>
</svg>
