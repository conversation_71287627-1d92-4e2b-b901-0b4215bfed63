<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <g transform="translate(960, 120)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
      现场示范：5 Why 提问法
    </text>
  </g>
  
  <!-- 提问流程 -->
  <g transform="translate(960, 400)">
    <!-- 表面问题 -->
    <g transform="translate(0, -250)">
      <rect x="-200" y="-30" width="400" height="60" fill="#95a5a6" rx="30"/>
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" font-weight="bold" fill="#ffffff">
        表面问题："店里没客流。"
      </text>
    </g>
    
    <!-- Why 1 -->
    <g transform="translate(0, -150)">
      <circle cx="-250" cy="0" r="25" fill="#e74c3c"/>
      <text x="-250" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" font-weight="bold" fill="#ffffff">
        Why1?
      </text>
      
      <path d="M -220 0 L -50 0" stroke="#e74c3c" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
      
      <rect x="-40" y="-25" width="280" height="50" fill="#e8f6f3" rx="25"/>
      <text x="100" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="22" fill="#2c3e50">
        "赚不到钱。"
      </text>
    </g>
    
    <!-- Why 2 -->
    <g transform="translate(0, -50)">
      <circle cx="-250" cy="0" r="25" fill="#f39c12"/>
      <text x="-250" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" font-weight="bold" fill="#ffffff">
        Why2?
      </text>
      
      <path d="M -220 0 L -50 0" stroke="#f39c12" stroke-width="3" fill="none" marker-end="url(#arrowhead2)"/>
      
      <rect x="-40" y="-25" width="280" height="50" fill="#fef9e7" rx="25"/>
      <text x="100" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="22" fill="#2c3e50">
        "每个月都在亏钱。"
      </text>
    </g>
    
    <!-- Why 3 -->
    <g transform="translate(0, 50)">
      <circle cx="-250" cy="0" r="25" fill="#3498db"/>
      <text x="-250" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" font-weight="bold" fill="#ffffff">
        Why3?
      </text>
      
      <path d="M -220 0 L -50 0" stroke="#3498db" stroke-width="3" fill="none" marker-end="url(#arrowhead3)"/>
      
      <rect x="-40" y="-25" width="280" height="50" fill="#ebf3fd" rx="25"/>
      <text x="100" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="22" fill="#2c3e50">
        "可能会血本无归。"
      </text>
    </g>
    
    <!-- Why 4 -->
    <g transform="translate(0, 150)">
      <circle cx="-250" cy="0" r="25" fill="#9b59b6"/>
      <text x="-250" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" font-weight="bold" fill="#ffffff">
        Why4?
      </text>
      
      <path d="M -220 0 L -50 0" stroke="#9b59b6" stroke-width="3" fill="none" marker-end="url(#arrowhead4)"/>
      
      <rect x="-40" y="-25" width="280" height="50" fill="#f4f1f8" rx="25"/>
      <text x="100" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="22" fill="#2c3e50">
        "无法对家人交代。"
      </text>
    </g>
    
    <!-- Why 5 - 核心痛点 -->
    <g transform="translate(0, 250)">
      <circle cx="-250" cy="0" r="25" fill="#27ae60"/>
      <text x="-250" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" font-weight="bold" fill="#ffffff">
        Why5?
      </text>
      
      <path d="M -220 0 L -50 0" stroke="#27ae60" stroke-width="3" fill="none" marker-end="url(#arrowhead5)"/>
      
      <!-- 突出显示核心痛点 -->
      <rect x="-50" y="-35" width="300" height="70" fill="#e74c3c" rx="35"/>
      <rect x="-40" y="-25" width="280" height="50" fill="#c0392b" rx="25"/>
      <text x="100" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" font-weight="bold" fill="#ffffff">
        "感觉自己是个失败者。"
      </text>
      
      <!-- 强调光环 -->
      <circle cx="100" cy="0" r="180" fill="none" stroke="#e74c3c" stroke-width="3" opacity="0.3"/>
      <circle cx="100" cy="0" r="200" fill="none" stroke="#e74c3c" stroke-width="2" opacity="0.2"/>
    </g>
  </g>
  
  <!-- 结论 -->
  <g transform="translate(960, 850)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="32" font-weight="bold" fill="#e74c3c">
      从"事实之痛"到"情感之痛"，这才是钉子要扎的地方。
    </text>
  </g>
  
  <!-- 装饰性挖掘图标 -->
  <g transform="translate(200, 300)" opacity="0.2">
    <!-- 铲子 -->
    <rect x="-5" y="-80" width="10" height="120" fill="#8e44ad" rx="2"/>
    <path d="M -15 -90 L 15 -90 L 10 -70 L -10 -70 Z" fill="#9b59b6"/>
    <!-- 土堆 -->
    <ellipse cx="30" cy="40" rx="25" ry="15" fill="#d4a574"/>
    <ellipse cx="50" cy="35" rx="20" ry="12" fill="#c19a5b"/>
  </g>
  
  <g transform="translate(1720, 600)" opacity="0.2">
    <!-- 铲子 -->
    <rect x="-5" y="-80" width="10" height="120" fill="#8e44ad" rx="2"/>
    <path d="M -15 -90 L 15 -90 L 10 -70 L -10 -70 Z" fill="#9b59b6"/>
    <!-- 土堆 -->
    <ellipse cx="30" cy="40" rx="25" ry="15" fill="#d4a574"/>
    <ellipse cx="50" cy="35" rx="20" ry="12" fill="#c19a5b"/>
  </g>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c"/>
    </marker>
    <marker id="arrowhead2" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#f39c12"/>
    </marker>
    <marker id="arrowhead3" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#3498db"/>
    </marker>
    <marker id="arrowhead4" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#9b59b6"/>
    </marker>
    <marker id="arrowhead5" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#27ae60"/>
    </marker>
  </defs>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.3">
    <path d="M 100 1000 Q 960 950 1820 1000" stroke="#e74c3c" stroke-width="4" fill="none"/>
  </g>
</svg>
