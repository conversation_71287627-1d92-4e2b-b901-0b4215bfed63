<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.r/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 沙漠背景（简化版） -->
  <rect x="0" y="800" width="1920" height="280" fill="#DEB887"/>
  <path d="M 0 850 Q 400 800 800 850 Q 1200 820 1920 850 L 1920 1080 L 0 1080 Z" fill="#D2B48C"/>
  
  <!-- 左侧：向导A -->
  <g transform="translate(480, 540)">
    <!-- 背景区域 -->
    <rect x="-350" y="-350" width="700" height="700" fill="#ecf0f1" rx="30"/>
    <rect x="-330" y="-330" width="660" height="660" fill="#bdc3c7" opacity="0.3" rx="25"/>
    
    <!-- 向导A人物 -->
    <g transform="translate(0, -150)">
      <circle cx="0" cy="-40" r="25" fill="#34495e"/>
      <rect x="-20" y="-15" width="40" height="60" fill="#34495e" rx="8"/>
      <rect x="-25" y="45" width="20" height="50" fill="#34495e"/>
      <rect x="5" y="45" width="20" height="50" fill="#34495e"/>
      
      <!-- 书本道具 -->
      <rect x="-35" y="-5" width="25" height="35" fill="#e74c3c" rx="3"/>
      <rect x="-32" y="-2" width="19" height="29" fill="#c0392b" rx="2"/>
      <line x1="-28" y1="5" x2="-18" y2="5" stroke="#ffffff" stroke-width="1"/>
      <line x1="-28" y1="10" x2="-18" y2="10" stroke="#ffffff" stroke-width="1"/>
      <line x1="-28" y1="15" x2="-18" y2="15" stroke="#ffffff" stroke-width="1"/>
    </g>
    
    <!-- 身份标签 -->
    <text x="0" y="-50" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" font-weight="bold" fill="#e74c3c">
      身份：知识渊博的"讲解员"
    </text>
    
    <!-- 方案 -->
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="20" font-weight="bold" fill="#2c3e50">
      方案：
    </text>
    <text x="0" y="30" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" fill="#7f8c8d">
      "我会把关于沙漠的所有知识
    </text>
    <text x="0" y="55" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" fill="#7f8c8d">
      都教给你，你学完就懂了。"
    </text>
    
    <!-- 模式 -->
    <text x="0" y="100" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="20" font-weight="bold" fill="#2c3e50">
      模式：给你一堆"食材"（知识）
    </text>
    
    <!-- 感受 -->
    <g transform="translate(0, 180)">
      <rect x="-100" y="-25" width="200" height="50" fill="#e74c3c" rx="25"/>
      <text x="0" y="-5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" font-weight="bold" fill="#ffffff">
        你的感受：
      </text>
      <text x="0" y="15" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" font-weight="bold" fill="#ffffff">
        更加焦虑
      </text>
    </g>
    
    <!-- 知识堆积图标 -->
    <g transform="translate(0, 280)" opacity="0.6">
      <rect x="-40" y="-20" width="80" height="40" fill="#95a5a6" rx="5"/>
      <rect x="-35" y="-35" width="70" height="30" fill="#7f8c8d" rx="5"/>
      <rect x="-30" y="-50" width="60" height="30" fill="#95a5a6" rx="5"/>
      <text x="0" y="-25" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="12" fill="#ffffff">
        知识山
      </text>
    </g>
  </g>
  
  <!-- 右侧：向导B -->
  <g transform="translate(1440, 540)">
    <!-- 背景区域 -->
    <rect x="-350" y="-350" width="700" height="700" fill="#e8f6f3" rx="30"/>
    <rect x="-330" y="-330" width="660" height="660" fill="#d5f4e6" opacity="0.5" rx="25"/>
    
    <!-- 向导B人物 -->
    <g transform="translate(0, -150)">
      <circle cx="0" cy="-40" r="25" fill="#27ae60"/>
      <rect x="-20" y="-15" width="40" height="60" fill="#27ae60" rx="8"/>
      <rect x="-25" y="45" width="20" height="50" fill="#27ae60"/>
      <rect x="5" y="45" width="20" height="50" fill="#27ae60"/>
      
      <!-- 地图道具 -->
      <rect x="10" y="-10" width="30" height="40" fill="#f39c12" rx="3"/>
      <rect x="12" y="-8" width="26" height="36" fill="#f1c40f" rx="2"/>
      <!-- 地图路线 -->
      <circle cx="18" cy="0" r="2" fill="#e74c3c"/>
      <circle cx="28" cy="10" r="2" fill="#3498db"/>
      <circle cx="32" cy="20" r="2" fill="#27ae60"/>
      <path d="M 18 0 Q 23 5 28 10 Q 30 15 32 20" stroke="#2c3e50" stroke-width="1" fill="none"/>
    </g>
    
    <!-- 身份标签 -->
    <text x="0" y="-50" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" font-weight="bold" fill="#27ae60">
      身份：值得信赖的"领路人"
    </text>
    
    <!-- 方案 -->
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="20" font-weight="bold" fill="#2c3e50">
      方案：
    </text>
    <text x="0" y="30" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" fill="#7f8c8d">
      "看着这张地图，我们现在在这，
    </text>
    <text x="0" y="55" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" fill="#7f8c8d">
      目标在那，只需要三步..."
    </text>
    
    <!-- 模式 -->
    <text x="0" y="100" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="20" font-weight="bold" fill="#2c3e50">
      模式：给你一张"地图"（路径）
    </text>
    
    <!-- 感受 -->
    <g transform="translate(0, 180)">
      <rect x="-100" y="-25" width="200" height="50" fill="#27ae60" rx="25"/>
      <text x="0" y="-5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" font-weight="bold" fill="#ffffff">
        你的感受：
      </text>
      <text x="0" y="15" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" font-weight="bold" fill="#ffffff">
        充满希望
      </text>
    </g>
    
    <!-- 地图路径图标 -->
    <g transform="translate(0, 280)" opacity="0.8">
      <rect x="-50" y="-30" width="100" height="60" fill="#f39c12" rx="8"/>
      <rect x="-45" y="-25" width="90" height="50" fill="#f1c40f" rx="5"/>
      
      <!-- 路径 -->
      <circle cx="-30" cy="-10" r="4" fill="#e74c3c"/>
      <circle cx="0" cy="0" r="4" fill="#3498db"/>
      <circle cx="30" cy="-5" r="4" fill="#27ae60"/>
      <path d="M -30 -10 Q -15 -5 0 0 Q 15 -2 30 -5" stroke="#2c3e50" stroke-width="2" fill="none"/>
      
      <text x="0" y="45" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="12" fill="#2c3e50">
        清晰路径
      </text>
    </g>
  </g>
  
  <!-- 中间VS符号 -->
  <g transform="translate(960, 400)">
    <circle cx="0" cy="0" r="60" fill="#e74c3c"/>
    <text x="0" y="15" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="36" font-weight="bold" fill="#ffffff">
      VS
    </text>
  </g>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.3">
    <path d="M 100 950 Q 960 900 1820 950" stroke="#3498db" stroke-width="4" fill="none"/>
  </g>
</svg>
