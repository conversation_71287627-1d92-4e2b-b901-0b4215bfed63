<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <g transform="translate(960, 120)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
      小组演练："毕业演说"
    </text>
  </g>
  
  <!-- 主视觉：毕业典礼演讲场景 -->
  <g transform="translate(960, 400)">
    <!-- 讲台 -->
    <g transform="translate(0, 100)">
      <rect x="-150" y="0" width="300" height="80" fill="#8e44ad" rx="10"/>
      <rect x="-140" y="10" width="280" height="60" fill="#9b59b6" rx="8"/>
    </g>
    
    <!-- 演讲者 -->
    <g transform="translate(0, 0)">
      <!-- 头部 -->
      <circle cx="0" cy="-30" r="25" fill="#e74c3c"/>
      <circle cx="0" cy="-30" r="20" fill="#c0392b"/>
      
      <!-- 学士帽 -->
      <rect x="-20" y="-55" width="40" height="8" fill="#2c3e50" rx="2"/>
      <rect x="-25" y="-60" width="50" height="5" fill="#34495e" rx="1"/>
      
      <!-- 流苏 -->
      <g transform="translate(25, -55)">
        <line x1="0" y1="0" x2="0" y2="15" stroke="#f1c40f" stroke-width="3"/>
        <circle cx="0" cy="15" r="3" fill="#f39c12"/>
      </g>
      
      <!-- 身体 -->
      <rect x="-20" y="-5" width="40" height="70" fill="#2c3e50" rx="8"/>
      <rect x="-18" y="-3" width="36" height="66" fill="#34495e" rx="6"/>
      
      <!-- 演讲手势 -->
      <rect x="-50" y="10" width="30" height="10" fill="#e74c3c" rx="5" transform="rotate(-20 -35 15)"/>
      <rect x="20" y="10" width="30" height="10" fill="#e74c3c" rx="5" transform="rotate(20 35 15)"/>
      
      <!-- 腿部 -->
      <rect x="-25" y="65" width="18" height="35" fill="#2c3e50"/>
      <rect x="7" y="65" width="18" height="35" fill="#2c3e50"/>
      
      <!-- 演讲激情的光环 -->
      <g opacity="0.6">
        <circle cx="0" cy="-30" r="60" fill="none" stroke="#f1c40f" stroke-width="3"/>
        <circle cx="0" cy="-30" r="80" fill="none" stroke="#f39c12" stroke-width="2"/>
      </g>
    </g>
    
    <!-- 观众席 -->
    <g transform="translate(0, 200)" opacity="0.4">
      <!-- 观众剪影 -->
      <g transform="translate(-200, 0)">
        <circle cx="0" cy="-15" r="12" fill="#7f8c8d"/>
        <rect x="-10" y="-3" width="20" height="25" fill="#7f8c8d" rx="3"/>
      </g>
      
      <g transform="translate(-100, 0)">
        <circle cx="0" cy="-15" r="12" fill="#7f8c8d"/>
        <rect x="-10" y="-3" width="20" height="25" fill="#7f8c8d" rx="3"/>
      </g>
      
      <g transform="translate(0, 0)">
        <circle cx="0" cy="-15" r="12" fill="#7f8c8d"/>
        <rect x="-10" y="-3" width="20" height="25" fill="#7f8c8d" rx="3"/>
      </g>
      
      <g transform="translate(100, 0)">
        <circle cx="0" cy="-15" r="12" fill="#7f8c8d"/>
        <rect x="-10" y="-3" width="20" height="25" fill="#7f8c8d" rx="3"/>
      </g>
      
      <g transform="translate(200, 0)">
        <circle cx="0" cy="-15" r="12" fill="#7f8c8d"/>
        <rect x="-10" y="-3" width="20" height="25" fill="#7f8c8d" rx="3"/>
      </g>
    </g>
  </g>
  
  <!-- 反馈规则 -->
  <g transform="translate(960, 650)">
    <rect x="-450" y="-80" width="900" height="160" fill="#f8f9fa" rx="20"/>
    <rect x="-430" y="-60" width="860" height="120" fill="#e9ecef" opacity="0.5" rx="15"/>
    
    <text x="0" y="-30" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" font-weight="bold" fill="#2c3e50">
      听众反馈两个问题：
    </text>
    
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="22" fill="#e74c3c">
      1. 听完后，我是信心满满了，还是依然焦虑？
    </text>
    
    <text x="0" y="35" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="22" fill="#27ae60">
      2. 听完后，我清晰地知道，接下来第一件该干的小事是什么了吗？
    </text>
  </g>
  
  <!-- 时间提示 -->
  <g transform="translate(960, 850)">
    <circle cx="0" cy="0" r="50" fill="#3498db" opacity="0.1"/>
    <circle cx="0" cy="0" r="35" fill="#2980b9" opacity="0.2"/>
    <text x="0" y="8" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="36" font-weight="bold" fill="#3498db">
      20:00
    </text>
  </g>
  
  <!-- 装饰性毕业元素 -->
  <g opacity="0.2">
    <!-- 左侧毕业证书 -->
    <g transform="translate(200, 500)">
      <rect x="-30" y="-20" width="60" height="40" fill="#f1c40f" rx="3"/>
      <rect x="-25" y="-15" width="50" height="30" fill="#f39c12" rx="2"/>
      
      <!-- 证书内容线条 -->
      <line x1="-20" y1="-8" x2="20" y2="-8" stroke="#ffffff" stroke-width="1"/>
      <line x1="-20" y1="-2" x2="20" y2="-2" stroke="#ffffff" stroke-width="1"/>
      <line x1="-20" y1="4" x2="15" y2="4" stroke="#ffffff" stroke-width="1"/>
      
      <!-- 印章 -->
      <circle cx="15" cy="10" r="5" fill="#e74c3c"/>
    </g>
    
    <!-- 右侧掌声 -->
    <g transform="translate(1720, 450)">
      <!-- 拍手图标 -->
      <g>
        <ellipse cx="-10" cy="0" rx="12" ry="8" fill="#f39c12" transform="rotate(-20 -10 0)"/>
        <ellipse cx="10" cy="0" rx="12" ry="8" fill="#e67e22" transform="rotate(20 10 0)"/>
      </g>
      
      <!-- 掌声波纹 -->
      <g opacity="0.6">
        <circle cx="0" cy="0" r="20" fill="none" stroke="#f1c40f" stroke-width="2"/>
        <circle cx="0" cy="0" r="30" fill="none" stroke="#f39c12" stroke-width="1"/>
      </g>
    </g>
  </g>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.3">
    <path d="M 100 950 Q 960 900 1820 950" stroke="#3498db" stroke-width="4" fill="none"/>
  </g>
</svg>
