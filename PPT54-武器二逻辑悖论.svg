<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <g transform="translate(960, 120)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
      武器二：逻辑悖论
    </text>
  </g>
  
  <!-- 主视觉：衔着自己尾巴的蛇 -->
  <g transform="translate(960, 350)">
    <!-- 蛇身主体 -->
    <g>
      <!-- 蛇身圆环 -->
      <circle cx="0" cy="0" r="100" fill="none" stroke="#f39c12" stroke-width="20"/>
      <circle cx="0" cy="0" r="100" fill="none" stroke="#e67e22" stroke-width="15"/>
      
      <!-- 蛇头 -->
      <g transform="translate(100, 0)">
        <ellipse cx="0" cy="0" rx="25" ry="15" fill="#f39c12"/>
        <ellipse cx="0" cy="0" rx="20" ry="12" fill="#e67e22"/>
        
        <!-- 眼睛 -->
        <circle cx="8" cy="-5" r="3" fill="#2c3e50"/>
        <circle cx="8" cy="5" r="3" fill="#2c3e50"/>
        <circle cx="9" cy="-5" r="1" fill="#ffffff"/>
        <circle cx="9" cy="5" r="1" fill="#ffffff"/>
        
        <!-- 嘴巴（咬着尾巴） -->
        <path d="M 20 0 Q 30 -5 35 0 Q 30 5 20 0" fill="#d35400"/>
      </g>
      
      <!-- 蛇尾 -->
      <g transform="translate(-100, 0)">
        <ellipse cx="0" cy="0" rx="15" ry="8" fill="#f39c12"/>
        <ellipse cx="0" cy="0" rx="12" ry="6" fill="#e67e22"/>
        
        <!-- 尾巴尖端 -->
        <ellipse cx="-10" cy="0" rx="8" ry="4" fill="#d35400"/>
      </g>
      
      <!-- 蛇身纹理 -->
      <g opacity="0.6">
        <circle cx="0" cy="-100" r="8" fill="#d35400"/>
        <circle cx="70" cy="-70" r="8" fill="#d35400"/>
        <circle cx="70" cy="70" r="8" fill="#d35400"/>
        <circle cx="0" cy="100" r="8" fill="#d35400"/>
        <circle cx="-70" cy="70" r="8" fill="#d35400"/>
        <circle cx="-70" cy="-70" r="8" fill="#d35400"/>
      </g>
    </g>
    
    <!-- 无限循环符号 -->
    <g transform="translate(0, 0)" opacity="0.4">
      <text x="0" y="10" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="60" fill="#e74c3c">
        ∞
      </text>
    </g>
  </g>
  
  <!-- 核心句式 -->
  <g transform="translate(960, 550)">
    <rect x="-450" y="-40" width="900" height="80" fill="#fef5e7" rx="20"/>
    <rect x="-430" y="-20" width="860" height="40" fill="#fdeaa7" rx="15"/>
    
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" font-weight="bold" fill="#f39c12">
      "一方面我们强调...，另一方面我们又在做...，这本身就是一个悖论。"
    </text>
  </g>
  
  <!-- 案例 -->
  <g transform="translate(960, 700)">
    <!-- 案例框 -->
    <rect x="-450" y="-100" width="900" height="200" fill="#ebf3fd" rx="20"/>
    <rect x="-430" y="-80" width="860" height="160" fill="#c8e6c9" opacity="0.3" rx="15"/>
    
    <!-- 案例标题 -->
    <text x="0" y="-50" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" font-weight="bold" fill="#3498db">
      案例：企业创新
    </text>
    
    <!-- 旧神 -->
    <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="22" fill="#2c3e50">
      旧神：我们鼓励创新试错。
    </text>
    
    <!-- 攻击 -->
    <text x="0" y="20" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="20" fill="#e74c3c">
      攻击：同时又要求每个项目都必须有确保盈利的ROI报告。
    </text>
    <text x="0" y="50" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="20" fill="#e74c3c">
      这本身就是一个让创新寸步难行的逻辑悖论。
    </text>
  </g>
  
  <!-- 装饰性悖论元素 -->
  <g opacity="0.2">
    <!-- 左侧矛盾箭头 -->
    <g transform="translate(200, 400)">
      <path d="M -30 0 L 30 0" stroke="#e74c3c" stroke-width="4" marker-end="url(#arrowhead1)"/>
      <path d="M 30 20 L -30 20" stroke="#3498db" stroke-width="4" marker-end="url(#arrowhead2)"/>
      
      <!-- 冲突标记 -->
      <circle cx="0" cy="10" r="15" fill="#f39c12" opacity="0.3"/>
      <path d="M -8 2 L 8 18 M 8 2 L -8 18" stroke="#f39c12" stroke-width="3"/>
    </g>
    
    <!-- 右侧矛盾箭头 -->
    <g transform="translate(1720, 500)">
      <path d="M -25 0 L 25 0" stroke="#27ae60" stroke-width="4" marker-end="url(#arrowhead3)"/>
      <path d="M 25 15 L -25 15" stroke="#9b59b6" stroke-width="4" marker-end="url(#arrowhead4)"/>
      
      <!-- 冲突标记 -->
      <circle cx="0" cy="7" r="12" fill="#e74c3c" opacity="0.3"/>
      <path d="M -6 1 L 6 13 M 6 1 L -6 13" stroke="#e74c3c" stroke-width="2"/>
    </g>
  </g>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="arrowhead1" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c"/>
    </marker>
    <marker id="arrowhead2" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#3498db"/>
    </marker>
    <marker id="arrowhead3" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#27ae60"/>
    </marker>
    <marker id="arrowhead4" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#9b59b6"/>
    </marker>
  </defs>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.3">
    <path d="M 100 950 Q 960 900 1820 950" stroke="#f39c12" stroke-width="4" fill="none"/>
  </g>
</svg>
