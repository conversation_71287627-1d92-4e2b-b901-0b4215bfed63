<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <g transform="translate(960, 120)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
      第四剑：卖一致
    </text>
  </g>
  
  <!-- 主视觉：钥匙完美插入锁 -->
  <g transform="translate(960, 400)">
    <!-- 锁 -->
    <g transform="translate(150, 0)">
      <rect x="-40" y="-60" width="80" height="120" fill="#34495e" rx="15"/>
      <rect x="-35" y="-55" width="70" height="110" fill="#2c3e50" rx="12"/>
      
      <!-- 锁孔 -->
      <circle cx="0" cy="-10" r="15" fill="#7f8c8d"/>
      <rect x="-5" y="-10" width="10" height="30" fill="#7f8c8d"/>
      
      <!-- 锁上的标签 -->
      <rect x="-25" y="70" width="50" height="20" fill="#f39c12" rx="5"/>
      <text x="0" y="85" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="12" font-weight="bold" fill="#ffffff">
        高阶课
      </text>
    </g>
    
    <!-- 钥匙 -->
    <g transform="translate(-150, 0)">
      <!-- 钥匙头部 -->
      <circle cx="0" cy="0" r="25" fill="#f1c40f"/>
      <circle cx="0" cy="0" r="20" fill="#f39c12"/>
      <circle cx="0" cy="0" r="10" fill="#e67e22"/>
      
      <!-- 钥匙柄 -->
      <rect x="25" y="-5" width="100" height="10" fill="#f1c40f" rx="5"/>
      <rect x="27" y="-3" width="96" height="6" fill="#f39c12" rx="3"/>
      
      <!-- 钥匙齿 -->
      <rect x="125" y="5" width="15" height="8" fill="#f1c40f"/>
      <rect x="125" y="13" width="10" height="6" fill="#f1c40f"/>
      <rect x="125" y="19" width="12" height="5" fill="#f1c40f"/>
      
      <!-- 钥匙上的标签 -->
      <rect x="-25" y="35" width="50" height="20" fill="#3498db" rx="5"/>
      <text x="0" y="50" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="12" font-weight="bold" fill="#ffffff">
        入门课
      </text>
    </g>
    
    <!-- 完美匹配的连接线 -->
    <g opacity="0.6">
      <defs>
        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
          <polygon points="0 0, 10 3.5, 0 7" fill="#27ae60"/>
        </marker>
      </defs>
      
      <path d="M -50 0 L 50 0" stroke="#27ae60" stroke-width="6" fill="none" marker-end="url(#arrowhead)"/>
      
      <!-- 匹配标识 -->
      <g transform="translate(0, -50)">
        <circle cx="0" cy="0" r="20" fill="#27ae60"/>
        <path d="M -8 0 L -3 5 L 8 -7" stroke="#ffffff" stroke-width="3" fill="none"/>
      </g>
    </g>
    
    <!-- 光芒效果 -->
    <g opacity="0.8">
      <line x1="0" y1="-100" x2="0" y2="-120" stroke="#f1c40f" stroke-width="4"/>
      <line x1="71" y1="-71" x2="85" y2="-85" stroke="#f1c40f" stroke-width="4"/>
      <line x1="100" y1="0" x2="120" y2="0" stroke="#f1c40f" stroke-width="4"/>
      <line x1="71" y1="71" x2="85" y2="85" stroke="#f1c40f" stroke-width="4"/>
      <line x1="0" y1="100" x2="0" y2="120" stroke="#f1c40f" stroke-width="4"/>
      <line x1="-71" y1="71" x2="-85" y2="85" stroke="#f1c40f" stroke-width="4"/>
      <line x1="-100" y1="0" x2="-120" y2="0" stroke="#f1c40f" stroke-width="4"/>
      <line x1="-71" y1="-71" x2="-85" y2="-85" stroke="#f1c40f" stroke-width="4"/>
    </g>
  </g>
  
  <!-- 核心理念 -->
  <g transform="translate(960, 650)">
    <rect x="-450" y="-30" width="900" height="60" fill="#fef5e7" rx="30"/>
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" font-weight="bold" fill="#f39c12">
      你讲的内容，和你卖的产品，必须是高度统一的。
    </text>
  </g>
  
  <!-- 关系说明 -->
  <g transform="translate(960, 750)">
    <rect x="-500" y="-60" width="1000" height="120" fill="#f39c12" rx="20"/>
    
    <text x="-200" y="-20" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="20" font-weight="bold" fill="#ffffff">
      入门课 = 诊断 + 地图
    </text>
    
    <text x="200" y="-20" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="20" font-weight="bold" fill="#ffffff">
      高阶课 = 治疗 + 向导 + 越野车
    </text>
    
    <!-- 分隔线 -->
    <line x1="0" y1="-50" x2="0" y2="50" stroke="#ffffff" stroke-width="3"/>
  </g>
  
  <!-- 装饰性一致性元素 -->
  <g opacity="0.2">
    <!-- 左侧齿轮组 -->
    <g transform="translate(200, 500)">
      <!-- 大齿轮 -->
      <circle cx="0" cy="0" r="25" fill="#3498db"/>
      <circle cx="0" cy="0" r="18" fill="#2980b9"/>
      <circle cx="0" cy="0" r="8" fill="#1f4e79"/>
      
      <!-- 小齿轮 -->
      <circle cx="35" cy="0" r="15" fill="#27ae60"/>
      <circle cx="35" cy="0" r="10" fill="#2ecc71"/>
      <circle cx="35" cy="0" r="5" fill="#1e8449"/>
      
      <!-- 齿 -->
      <rect x="-3" y="-30" width="6" height="10" fill="#3498db"/>
      <rect x="-3" y="20" width="6" height="10" fill="#3498db"/>
      <rect x="-30" y="-3" width="10" height="6" fill="#3498db"/>
      <rect x="20" y="-3" width="10" height="6" fill="#3498db"/>
    </g>
    
    <!-- 右侧拼图 -->
    <g transform="translate(1720, 450)">
      <!-- 拼图片1 -->
      <path d="M 0 0 L 30 0 Q 35 0 35 5 L 35 25 Q 35 30 30 30 L 5 30 Q 0 30 0 25 L 0 5 Q 0 0 5 0 Z" fill="#e74c3c"/>
      
      <!-- 拼图片2 -->
      <path d="M 35 0 L 65 0 Q 70 0 70 5 L 70 25 Q 70 30 65 30 L 40 30 Q 35 30 35 25 L 35 5 Q 35 0 40 0 Z" fill="#27ae60"/>
    </g>
  </g>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.3">
    <path d="M 100 950 Q 960 900 1820 950" stroke="#f39c12" stroke-width="4" fill="none"/>
  </g>
</svg>
