<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 左侧：纪录片模式 -->
  <g transform="translate(480, 540)">
    <!-- 背景区域 -->
    <rect x="-400" y="-400" width="800" height="800" fill="#7f8c8d" rx="20"/>
    <rect x="-380" y="-380" width="760" height="760" fill="#95a5a6" rx="15"/>
    
    <!-- 标题 -->
    <text x="0" y="-300" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="48" font-weight="bold" fill="#ffffff">
      纪录片模式
    </text>
    
    <!-- 副标题 -->
    <text x="0" y="-240" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="32" fill="#ecf0f1">
      知识的呈现
    </text>
    
    <!-- 纪录片视觉元素 -->
    <g transform="translate(0, -50)">
      <!-- 老式摄像机 -->
      <rect x="-80" y="-60" width="160" height="120" rx="10" fill="#2c3e50"/>
      <circle cx="0" cy="0" r="40" fill="#34495e"/>
      <circle cx="0" cy="0" r="30" fill="#2c3e50"/>
      <circle cx="0" cy="0" r="20" fill="#34495e"/>
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="12" fill="#ffffff">
        REC
      </text>
      
      <!-- 胶片条 -->
      <rect x="-100" y="80" width="200" height="30" fill="#2c3e50" rx="5"/>
      <rect x="-90" y="85" width="20" height="20" fill="#ffffff" rx="2"/>
      <rect x="-60" y="85" width="20" height="20" fill="#ffffff" rx="2"/>
      <rect x="-30" y="85" width="20" height="20" fill="#ffffff" rx="2"/>
      <rect x="0" y="85" width="20" height="20" fill="#ffffff" rx="2"/>
      <rect x="30" y="85" width="20" height="20" fill="#ffffff" rx="2"/>
      <rect x="60" y="85" width="20" height="20" fill="#ffffff" rx="2"/>
    </g>
    
    <!-- 特征描述 -->
    <text x="0" y="200" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" fill="#ffffff">
      客观 · 全面 · 知识性强
    </text>
  </g>
  
  <!-- 中间VS符号 -->
  <g transform="translate(960, 540)">
    <circle cx="0" cy="0" r="80" fill="#e74c3c"/>
    <text x="0" y="15" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="48" font-weight="bold" fill="#ffffff">
      VS
    </text>
  </g>
  
  <!-- 右侧：大片模式 -->
  <g transform="translate(1440, 540)">
    <!-- 背景区域 -->
    <rect x="-400" y="-400" width="800" height="800" fill="#e67e22" rx="20"/>
    <rect x="-380" y="-380" width="760" height="760" fill="#f39c12" rx="15"/>
    
    <!-- 标题 -->
    <text x="0" y="-300" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="48" font-weight="bold" fill="#ffffff">
      大片模式
    </text>
    
    <!-- 副标题 -->
    <text x="0" y="-240" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="32" fill="#2c3e50">
      问题的解决
    </text>
    
    <!-- 大片视觉元素 -->
    <g transform="translate(0, -50)">
      <!-- 现代摄影机 -->
      <rect x="-100" y="-40" width="200" height="80" rx="15" fill="#2c3e50"/>
      <circle cx="-50" cy="0" r="25" fill="#34495e"/>
      <circle cx="-50" cy="0" r="18" fill="#e74c3c"/>
      <rect x="20" y="-15" width="60" height="30" rx="5" fill="#34495e"/>
      <text x="50" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="10" fill="#ffffff">
        4K
      </text>
      
      <!-- 动作线条 -->
      <path d="M -150 -20 L -120 -10 M -150 0 L -120 10 M -150 20 L -120 30" stroke="#e74c3c" stroke-width="3"/>
      <path d="M 150 -20 L 120 -10 M 150 0 L 120 10 M 150 20 L 120 30" stroke="#e74c3c" stroke-width="3"/>
      
      <!-- 爆炸效果 -->
      <g transform="translate(0, 80)">
        <path d="M 0 0 L -20 -15 L -10 -30 L 10 -30 L 20 -15 L 30 0 L 20 15 L 10 30 L -10 30 L -20 15 Z" fill="#e74c3c" opacity="0.7"/>
        <circle cx="0" cy="0" r="15" fill="#f39c12"/>
      </g>
    </g>
    
    <!-- 特征描述 -->
    <text x="0" y="200" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" fill="#2c3e50">
      震撼 · 解决 · 转化力强
    </text>
  </g>
  
  <!-- 底部对比说明 -->
  <g transform="translate(960, 950)">
    <text x="-300" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" fill="#7f8c8d">
      知识的呈现
    </text>
    <text x="300" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" fill="#e67e22">
      问题的解决
    </text>
  </g>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.2">
    <path d="M 100 100 Q 960 50 1820 100" stroke="#e74c3c" stroke-width="4" fill="none"/>
    <path d="M 100 1000 Q 960 1050 1820 1000" stroke="#f39c12" stroke-width="4" fill="none"/>
  </g>
</svg>
