<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <g transform="translate(960, 150)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
      本日核心使命
    </text>
  </g>
  
  <!-- 左侧：上午任务 -->
  <g transform="translate(480, 540)">
    <!-- 背景区域 -->
    <rect x="-350" y="-300" width="700" height="600" fill="#e8f5e8" rx="30"/>
    <rect x="-330" y="-280" width="660" height="560" fill="#c8e6c9" opacity="0.5" rx="25"/>
    
    <!-- 从"知道"到"做到"的箭头图标 -->
    <g transform="translate(0, -150)">
      <!-- 起点：知道 -->
      <g transform="translate(-80, 0)">
        <circle cx="0" cy="0" r="30" fill="#3498db"/>
        <circle cx="0" cy="0" r="25" fill="#2980b9"/>
        <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="14" font-weight="bold" fill="#ffffff">
          知道
        </text>
      </g>
      
      <!-- 箭头 -->
      <path d="M -50 0 L 50 0 M 35 -15 L 50 0 L 35 15" stroke="#27ae60" stroke-width="6" fill="none"/>
      
      <!-- 终点：做到 -->
      <g transform="translate(80, 0)">
        <circle cx="0" cy="0" r="30" fill="#27ae60"/>
        <circle cx="0" cy="0" r="25" fill="#2ecc71"/>
        <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="14" font-weight="bold" fill="#ffffff">
          做到
        </text>
      </g>
    </g>
    
    <!-- 标题 -->
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="36" font-weight="bold" fill="#27ae60">
      上午：完成"价值闭环"
    </text>
    
    <!-- 描述 -->
    <text x="0" y="60" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" fill="#2c3e50">
      掌握"可行动收场"的设计方法，
    </text>
    <text x="0" y="90" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" fill="#2c3e50">
      确保学员将课堂所学，转化为
    </text>
    <text x="0" y="120" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" fill="#2c3e50">
      课后实实在在的行动。
    </text>
  </g>
  
  <!-- 右侧：下午任务 -->
  <g transform="translate(1440, 540)">
    <!-- 背景区域 -->
    <rect x="-350" y="-300" width="700" height="600" fill="#fef5e7" rx="30"/>
    <rect x="-330" y="-280" width="660" height="560" fill="#fdeaa7" opacity="0.5" rx="25"/>
    
    <!-- 从"价值"到"价格"的循环箭头图标 -->
    <g transform="translate(0, -150)">
      <!-- 价值 -->
      <g transform="translate(-50, -30)">
        <rect x="-25" y="-15" width="50" height="30" fill="#3498db" rx="15"/>
        <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="12" font-weight="bold" fill="#ffffff">
          价值
        </text>
      </g>
      
      <!-- 价格 -->
      <g transform="translate(50, 30)">
        <rect x="-25" y="-15" width="50" height="30" fill="#f39c12" rx="15"/>
        <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="12" font-weight="bold" fill="#ffffff">
          价格
        </text>
      </g>
      
      <!-- 循环箭头 -->
      <g opacity="0.8">
        <path d="M -25 -15 Q 0 -50 25 -15" stroke="#27ae60" stroke-width="4" fill="none" marker-end="url(#arrowhead1)"/>
        <path d="M 25 15 Q 0 50 -25 15" stroke="#e74c3c" stroke-width="4" fill="none" marker-end="url(#arrowhead2)"/>
      </g>
    </g>
    
    <!-- 标题 -->
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="36" font-weight="bold" fill="#f39c12">
      下午：构建"商业闭环"
    </text>
    
    <!-- 描述 -->
    <text x="0" y="60" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" fill="#2c3e50">
      掌握"不销而销"的终极心法，
    </text>
    <text x="0" y="90" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" fill="#2c3e50">
      为课程设计一条优雅而高效的
    </text>
    <text x="0" y="120" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" fill="#2c3e50">
      商业转化路径。
    </text>
  </g>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="arrowhead1" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#27ae60"/>
    </marker>
    <marker id="arrowhead2" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c"/>
    </marker>
  </defs>
  
  <!-- 中间连接元素 -->
  <g transform="translate(960, 540)">
    <circle cx="0" cy="0" r="40" fill="#9b59b6"/>
    <text x="0" y="8" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" font-weight="bold" fill="#ffffff">
      +
    </text>
    
    <!-- 连接线 -->
    <path d="M -400 0 L -50 0" stroke="#27ae60" stroke-width="4" fill="none" opacity="0.6"/>
    <path d="M 50 0 L 400 0" stroke="#f39c12" stroke-width="4" fill="none" opacity="0.6"/>
  </g>
  
  <!-- 底部总结 -->
  <g transform="translate(960, 850)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="32" fill="#2c3e50">
      这是从"改变学员"到"成就自己"的完整旅程。
    </text>
  </g>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.3">
    <path d="M 100 950 Q 960 900 1820 950" stroke="#9b59b6" stroke-width="4" fill="none"/>
  </g>
</svg>
