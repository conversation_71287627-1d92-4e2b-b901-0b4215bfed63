<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <g transform="translate(960, 120)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
      武器一：数据反常
    </text>
  </g>
  
  <!-- 主视觉：向上增长的红色箭头突然断裂转向下方 -->
  <g transform="translate(960, 350)">
    <!-- 正常增长部分 -->
    <g>
      <path d="M -200 100 L -100 50 L 0 0" stroke="#27ae60" stroke-width="8" fill="none"/>
      <circle cx="-200" cy="100" r="8" fill="#27ae60"/>
      <circle cx="-100" cy="50" r="8" fill="#27ae60"/>
      <circle cx="0" cy="0" r="8" fill="#27ae60"/>
    </g>
    
    <!-- 断裂点 -->
    <g transform="translate(0, 0)">
      <circle cx="0" cy="0" r="15" fill="#e74c3c"/>
      <circle cx="0" cy="0" r="10" fill="#c0392b"/>
      
      <!-- 裂缝效果 -->
      <g opacity="0.8">
        <path d="M -10 -10 L 10 10" stroke="#ffffff" stroke-width="3"/>
        <path d="M 10 -10 L -10 10" stroke="#ffffff" stroke-width="3"/>
      </g>
    </g>
    
    <!-- 反常下降部分 -->
    <g>
      <path d="M 0 0 L 100 80 L 200 150" stroke="#e74c3c" stroke-width="8" fill="none"/>
      <circle cx="100" cy="80" r="8" fill="#e74c3c"/>
      <circle cx="200" cy="150" r="8" fill="#e74c3c"/>
      
      <!-- 下降箭头 -->
      <path d="M 180 130 L 200 150 L 170 140" stroke="#e74c3c" stroke-width="4" fill="none"/>
    </g>
    
    <!-- 冲击波效果 -->
    <g opacity="0.4">
      <circle cx="0" cy="0" r="80" fill="none" stroke="#f39c12" stroke-width="3"/>
      <circle cx="0" cy="0" r="100" fill="none" stroke="#f39c12" stroke-width="2"/>
      <circle cx="0" cy="0" r="120" fill="none" stroke="#f39c12" stroke-width="1"/>
    </g>
  </g>
  
  <!-- 核心句式 -->
  <g transform="translate(960, 550)">
    <rect x="-400" y="-40" width="800" height="80" fill="#f8f9fa" rx="20"/>
    <rect x="-380" y="-20" width="760" height="40" fill="#e9ecef" rx="15"/>
    
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="32" font-weight="bold" fill="#e74c3c">
      "我们通常认为...，但数据显示了一个惊人的结论..."
    </text>
  </g>
  
  <!-- 案例 -->
  <g transform="translate(960, 700)">
    <!-- 案例框 -->
    <rect x="-450" y="-100" width="900" height="200" fill="#e8f5e8" rx="20"/>
    <rect x="-430" y="-80" width="860" height="160" fill="#c8e6c9" opacity="0.5" rx="15"/>
    
    <!-- 案例标题 -->
    <text x="0" y="-50" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" font-weight="bold" fill="#27ae60">
      案例：团队管理
    </text>
    
    <!-- 旧神 -->
    <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="22" fill="#2c3e50">
      旧神：管理者应多帮助后进员工。
    </text>
    
    <!-- 攻击 -->
    <text x="0" y="30" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="22" fill="#e74c3c">
      攻击：盖洛普数据显示，顶级管理者把80%的时间花在最优秀的员工身上。
    </text>
  </g>
  
  <!-- 装饰性数据元素 -->
  <g opacity="0.2">
    <!-- 左侧数据图表 -->
    <g transform="translate(200, 400)">
      <rect x="-40" y="-30" width="80" height="60" fill="#27ae60" rx="5"/>
      <rect x="-35" y="-25" width="70" height="50" fill="#2ecc71" rx="3"/>
      
      <!-- 柱状图 -->
      <rect x="-25" y="5" width="8" height="15" fill="#ffffff"/>
      <rect x="-10" y="-5" width="8" height="25" fill="#ffffff"/>
      <rect x="5" y="10" width="8" height="10" fill="#ffffff"/>
      <rect x="20" y="-10" width="8" height="30" fill="#ffffff"/>
    </g>
    
    <!-- 右侧数据图表 -->
    <g transform="translate(1720, 500)">
      <rect x="-40" y="-30" width="80" height="60" fill="#e74c3c" rx="5"/>
      <rect x="-35" y="-25" width="70" height="50" fill="#c0392b" rx="3"/>
      
      <!-- 饼图 -->
      <circle cx="0" cy="0" r="15" fill="#ffffff"/>
      <path d="M 0 0 L 0 -15 A 15 15 0 0 1 10.6 -10.6 Z" fill="#f39c12"/>
      <path d="M 0 0 L 10.6 -10.6 A 15 15 0 0 1 10.6 10.6 Z" fill="#27ae60"/>
      <path d="M 0 0 L 10.6 10.6 A 15 15 0 1 1 0 -15 Z" fill="#3498db"/>
    </g>
  </g>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.3">
    <path d="M 100 950 Q 960 900 1820 950" stroke="#e74c3c" stroke-width="4" fill="none"/>
  </g>
</svg>
