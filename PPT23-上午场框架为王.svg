<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 建筑设计蓝图背景 -->
  <g opacity="0.1">
    <!-- 蓝图背景 -->
    <rect x="0" y="0" width="1920" height="1080" fill="#2980b9"/>
    
    <!-- 蓝图网格 -->
    <defs>
      <pattern id="blueprintGrid" x="0" y="0" width="50" height="50" patternUnits="userSpaceOnUse">
        <rect width="50" height="50" fill="none" stroke="#3498db" stroke-width="0.5" opacity="0.4"/>
      </pattern>
    </defs>
    <rect x="0" y="0" width="1920" height="1080" fill="url(#blueprintGrid)"/>
    
    <!-- 蓝图细节线条 -->
    <g opacity="0.3">
      <line x1="200" y1="200" x2="1720" y2="200" stroke="#3498db" stroke-width="2"/>
      <line x1="200" y1="400" x2="1720" y2="400" stroke="#3498db" stroke-width="2"/>
      <line x1="200" y1="600" x2="1720" y2="600" stroke="#3498db" stroke-width="2"/>
      <line x1="200" y1="800" x2="1720" y2="800" stroke="#3498db" stroke-width="2"/>
      
      <line x1="300" y1="100" x2="300" y2="900" stroke="#3498db" stroke-width="2"/>
      <line x1="600" y1="100" x2="600" y2="900" stroke="#3498db" stroke-width="2"/>
      <line x1="960" y1="100" x2="960" y2="900" stroke="#3498db" stroke-width="2"/>
      <line x1="1320" y1="100" x2="1320" y2="900" stroke="#3498db" stroke-width="2"/>
      <line x1="1620" y1="100" x2="1620" y2="900" stroke="#3498db" stroke-width="2"/>
    </g>
    
    <!-- 建筑轮廓 -->
    <g opacity="0.2">
      <rect x="400" y="300" width="1120" height="500" fill="none" stroke="#ffffff" stroke-width="4"/>
      <rect x="500" y="200" width="920" height="600" fill="none" stroke="#ffffff" stroke-width="3"/>
      <path d="M 500 200 L 960 100 L 1420 200 Z" fill="none" stroke="#ffffff" stroke-width="3"/>
    </g>
  </g>
  
  <!-- 时间标识 -->
  <g transform="translate(960, 250)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="48" font-weight="bold" fill="#ffffff">
      上午场 (09:00 - 12:00)
    </text>
  </g>
  
  <!-- 主标题 -->
  <g transform="translate(960, 400)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="72" font-weight="bold" fill="#ffffff">
      框架为王：3分钟赢得无条件信任
    </text>
  </g>
  
  <!-- 装饰性框架元素 -->
  <g opacity="0.4">
    <!-- 左侧框架 -->
    <g transform="translate(200, 500)">
      <rect x="0" y="0" width="200" height="300" fill="none" stroke="#ffffff" stroke-width="6"/>
      <rect x="20" y="20" width="160" height="260" fill="none" stroke="#ffffff" stroke-width="4"/>
      <line x1="0" y1="100" x2="200" y2="100" stroke="#ffffff" stroke-width="3"/>
      <line x1="0" y1="200" x2="200" y2="200" stroke="#ffffff" stroke-width="3"/>
      <line x1="67" y1="0" x2="67" y2="300" stroke="#ffffff" stroke-width="3"/>
      <line x1="133" y1="0" x2="133" y2="300" stroke="#ffffff" stroke-width="3"/>
    </g>
    
    <!-- 右侧框架 -->
    <g transform="translate(1520, 500)">
      <rect x="0" y="0" width="200" height="300" fill="none" stroke="#ffffff" stroke-width="6"/>
      <rect x="20" y="20" width="160" height="260" fill="none" stroke="#ffffff" stroke-width="4"/>
      <line x1="0" y1="100" x2="200" y2="100" stroke="#ffffff" stroke-width="3"/>
      <line x1="0" y1="200" x2="200" y2="200" stroke="#ffffff" stroke-width="3"/>
      <line x1="67" y1="0" x2="67" y2="300" stroke="#ffffff" stroke-width="3"/>
      <line x1="133" y1="0" x2="133" y2="300" stroke="#ffffff" stroke-width="3"/>
    </g>
  </g>
  
  <!-- 中央框架图标 -->
  <g transform="translate(960, 600)" opacity="0.6">
    <!-- 主框架 -->
    <rect x="-150" y="-100" width="300" height="200" fill="none" stroke="#ffffff" stroke-width="8"/>
    <rect x="-130" y="-80" width="260" height="160" fill="none" stroke="#ffffff" stroke-width="6"/>
    
    <!-- 内部分割 -->
    <line x1="-150" y1="-33" x2="150" y2="-33" stroke="#ffffff" stroke-width="4"/>
    <line x1="-150" y1="33" x2="150" y2="33" stroke="#ffffff" stroke-width="4"/>
    <line x1="-50" y1="-100" x2="-50" y2="100" stroke="#ffffff" stroke-width="4"/>
    <line x1="50" y1="-100" x2="50" y2="100" stroke="#ffffff" stroke-width="4"/>
    
    <!-- 核心点 -->
    <circle cx="0" cy="0" r="15" fill="#f39c12"/>
    <circle cx="0" cy="0" r="10" fill="#ffffff"/>
  </g>
  
  <!-- 底部说明 -->
  <g transform="translate(960, 850)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="32" fill="#ffffff">
      锻造矛杆——让你课程的价值变得可视化、结构化、可信赖
    </text>
  </g>
  
  <!-- 装饰性测量工具 -->
  <g transform="translate(300, 200)" opacity="0.3">
    <!-- 直角尺 -->
    <path d="M 0 0 L 60 0 L 60 10 L 10 10 L 10 60 L 0 60 Z" fill="#ffffff"/>
    <!-- 刻度 -->
    <line x1="10" y1="5" x2="15" y2="5" stroke="#2980b9" stroke-width="1"/>
    <line x1="20" y1="5" x2="25" y2="5" stroke="#2980b9" stroke-width="1"/>
    <line x1="30" y1="5" x2="35" y2="5" stroke="#2980b9" stroke-width="1"/>
    <line x1="5" y1="15" x2="5" y2="20" stroke="#2980b9" stroke-width="1"/>
    <line x1="5" y1="25" x2="5" y2="30" stroke="#2980b9" stroke-width="1"/>
    <line x1="5" y1="35" x2="5" y2="40" stroke="#2980b9" stroke-width="1"/>
  </g>
  
  <g transform="translate(1620, 300)" opacity="0.3">
    <!-- 圆规 -->
    <line x1="-30" y1="0" x2="30" y2="0" stroke="#ffffff" stroke-width="3"/>
    <circle cx="-30" cy="0" r="5" fill="#ffffff"/>
    <circle cx="30" cy="0" r="3" fill="#ffffff"/>
    <line x1="0" y1="-40" x2="0" y2="0" stroke="#ffffff" stroke-width="2"/>
  </g>
  
  <!-- 边框装饰 -->
  <rect x="40" y="40" width="1840" height="1000" fill="none" stroke="#ffffff" stroke-width="3" opacity="0.3" rx="20"/>
</svg>
