<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <g transform="translate(960, 120)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
      导演工具箱：三大黄金故事模型
    </text>
  </g>
  
  <!-- 胶片框一：个人英雄之旅 -->
  <g transform="translate(320, 450)">
    <!-- 胶片框 -->
    <rect x="-200" y="-150" width="400" height="300" fill="#2c3e50" rx="15"/>
    <rect x="-190" y="-140" width="380" height="280" fill="#34495e" rx="10"/>
    <rect x="-180" y="-130" width="360" height="260" fill="#ecf0f1" rx="8"/>
    
    <!-- 胶片孔 -->
    <g opacity="0.6">
      <rect x="-210" y="-120" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="-210" y="-90" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="-210" y="-60" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="-210" y="-30" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="-210" y="0" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="-210" y="30" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="-210" y="60" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="-210" y="90" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="-210" y="120" width="20" height="15" fill="#2c3e50" rx="3"/>
      
      <rect x="190" y="-120" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="190" y="-90" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="190" y="-60" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="190" y="-30" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="190" y="0" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="190" y="30" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="190" y="60" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="190" y="90" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="190" y="120" width="20" height="15" fill="#2c3e50" rx="3"/>
    </g>
    
    <!-- 英雄图标 -->
    <g transform="translate(0, -50)">
      <circle cx="0" cy="-20" r="20" fill="#e74c3c"/>
      <rect x="-15" y="0" width="30" height="40" fill="#e74c3c" rx="5"/>
      
      <!-- 斗篷 -->
      <path d="M -15 5 Q -25 15 -20 40 Q -10 50 0 45 Q 10 50 20 40 Q 25 15 15 5" fill="#c0392b" opacity="0.8"/>
    </g>
    
    <text x="0" y="50" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" font-weight="bold" fill="#e74c3c">
      个人英雄之旅 (你的故事)
    </text>
    <text x="0" y="80" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" fill="#7f8c8d">
      作用：建立信任，拉近距离
    </text>
  </g>
  
  <!-- 胶片框二：客户见证故事 -->
  <g transform="translate(960, 450)">
    <!-- 胶片框 -->
    <rect x="-200" y="-150" width="400" height="300" fill="#2c3e50" rx="15"/>
    <rect x="-190" y="-140" width="380" height="280" fill="#34495e" rx="10"/>
    <rect x="-180" y="-130" width="360" height="260" fill="#ecf0f1" rx="8"/>
    
    <!-- 胶片孔 -->
    <g opacity="0.6">
      <rect x="-210" y="-120" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="-210" y="-90" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="-210" y="-60" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="-210" y="-30" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="-210" y="0" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="-210" y="30" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="-210" y="60" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="-210" y="90" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="-210" y="120" width="20" height="15" fill="#2c3e50" rx="3"/>
      
      <rect x="190" y="-120" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="190" y="-90" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="190" y="-60" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="190" y="-30" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="190" y="0" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="190" y="30" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="190" y="60" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="190" y="90" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="190" y="120" width="20" height="15" fill="#2c3e50" rx="3"/>
    </g>
    
    <!-- 客户见证图标 -->
    <g transform="translate(0, -50)">
      <!-- 两个人物 -->
      <g transform="translate(-30, 0)">
        <circle cx="0" cy="-15" r="15" fill="#27ae60"/>
        <rect x="-12" y="0" width="24" height="30" fill="#27ae60" rx="4"/>
      </g>
      
      <g transform="translate(30, 0)">
        <circle cx="0" cy="-15" r="15" fill="#2ecc71"/>
        <rect x="-12" y="0" width="24" height="30" fill="#2ecc71" rx="4"/>
      </g>
      
      <!-- 成功标记 -->
      <g transform="translate(0, -30)">
        <circle cx="0" cy="0" r="12" fill="#f1c40f"/>
        <path d="M -6 0 L -2 4 L 6 -4" stroke="#ffffff" stroke-width="3" fill="none"/>
      </g>
    </g>
    
    <text x="0" y="50" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" font-weight="bold" fill="#27ae60">
      客户见证故事 (他的故事)
    </text>
    <text x="0" y="80" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" fill="#7f8c8d">
      作用：提供佐证，激发信心
    </text>
  </g>
  
  <!-- 胶片框三：隐喻/类比故事 -->
  <g transform="translate(1600, 450)">
    <!-- 胶片框 -->
    <rect x="-200" y="-150" width="400" height="300" fill="#2c3e50" rx="15"/>
    <rect x="-190" y="-140" width="380" height="280" fill="#34495e" rx="10"/>
    <rect x="-180" y="-130" width="360" height="260" fill="#ecf0f1" rx="8"/>
    
    <!-- 胶片孔 -->
    <g opacity="0.6">
      <rect x="-210" y="-120" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="-210" y="-90" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="-210" y="-60" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="-210" y="-30" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="-210" y="0" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="-210" y="30" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="-210" y="60" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="-210" y="90" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="-210" y="120" width="20" height="15" fill="#2c3e50" rx="3"/>
      
      <rect x="190" y="-120" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="190" y="-90" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="190" y="-60" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="190" y="-30" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="190" y="0" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="190" y="30" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="190" y="60" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="190" y="90" width="20" height="15" fill="#2c3e50" rx="3"/>
      <rect x="190" y="120" width="20" height="15" fill="#2c3e50" rx="3"/>
    </g>
    
    <!-- 隐喻图标 -->
    <g transform="translate(0, -50)">
      <!-- 灯泡 -->
      <ellipse cx="0" cy="-10" rx="20" ry="25" fill="#f1c40f"/>
      <ellipse cx="0" cy="-10" rx="15" ry="20" fill="#f39c12"/>
      
      <!-- 灯泡底座 -->
      <rect x="-8" y="15" width="16" height="8" fill="#95a5a6" rx="2"/>
      <rect x="-6" y="23" width="12" height="6" fill="#7f8c8d" rx="1"/>
      
      <!-- 光芒 -->
      <g opacity="0.6">
        <line x1="0" y1="-45" x2="0" y2="-55" stroke="#f1c40f" stroke-width="3"/>
        <line x1="32" y1="-32" x2="42" y2="-42" stroke="#f1c40f" stroke-width="3"/>
        <line x1="45" y1="0" x2="55" y2="0" stroke="#f1c40f" stroke-width="3"/>
        <line x1="32" y1="32" x2="42" y2="42" stroke="#f1c40f" stroke-width="3"/>
        <line x1="-32" y1="-32" x2="-42" y2="-42" stroke="#f1c40f" stroke-width="3"/>
        <line x1="-45" y1="0" x2="-55" y2="0" stroke="#f1c40f" stroke-width="3"/>
        <line x1="-32" y1="32" x2="-42" y2="42" stroke="#f1c40f" stroke-width="3"/>
      </g>
    </g>
    
    <text x="0" y="50" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" font-weight="bold" fill="#f39c12">
      隐喻/类比故事 (它的故事)
    </text>
    <text x="0" y="80" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" fill="#7f8c8d">
      作用：化繁为简，加深理解
    </text>
  </g>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.3">
    <path d="M 100 950 Q 960 900 1820 950" stroke="#2c3e50" stroke-width="4" fill="none"/>
  </g>
</svg>
