<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 火箭发射背景 -->
  <g opacity="0.15">
    <!-- 发射台 -->
    <rect x="800" y="700" width="320" height="380" fill="#34495e"/>
    <rect x="820" y="720" width="280" height="360" fill="#2c3e50"/>
    
    <!-- 火箭主体 -->
    <g transform="translate(960, 500)">
      <!-- 火箭头部 -->
      <path d="M 0 -200 L -50 -120 L 50 -120 Z" fill="#e74c3c"/>
      <path d="M 0 -190 L -40 -125 L 40 -125 Z" fill="#c0392b"/>
      
      <!-- 火箭身体 -->
      <rect x="-50" y="-120" width="100" height="320" fill="#ecf0f1" rx="8"/>
      <rect x="-45" y="-115" width="90" height="310" fill="#bdc3c7" rx="6"/>
      
      <!-- 火箭推进器 -->
      <g transform="translate(0, 200)">
        <rect x="-60" y="0" width="120" height="80" fill="#34495e" rx="10"/>
        <rect x="-55" y="5" width="110" height="70" fill="#2c3e50" rx="8"/>
        
        <!-- 巨大火焰 -->
        <g transform="translate(0, 80)">
          <ellipse cx="0" cy="30" rx="80" ry="40" fill="#e67e22"/>
          <path d="M -70 30 Q -35 -60 0 30 Q 35 -60 70 30" fill="#e74c3c"/>
          <path d="M -50 25 Q -25 -80 25 25 Q 50 -80 50 25" fill="#f39c12"/>
          <path d="M -30 20 Q 0 -100 30 20" fill="#f1c40f"/>
          <path d="M -20 15 Q 0 -120 20 15" fill="#ffffff" opacity="0.8"/>
          
          <!-- 火花效果 -->
          <g opacity="0.8">
            <circle cx="-90" cy="40" r="6" fill="#f39c12"/>
            <circle cx="90" cy="35" r="5" fill="#e74c3c"/>
            <circle cx="-80" cy="-30" r="5" fill="#f1c40f"/>
            <circle cx="80" cy="-25" r="6" fill="#e67e22"/>
            <circle cx="-100" cy="10" r="4" fill="#f39c12"/>
            <circle cx="100" cy="15" r="4" fill="#e74c3c"/>
          </g>
        </g>
      </g>
      
      <!-- 火箭标识 -->
      <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" fill="#2c3e50">
        转化引擎
      </text>
    </g>
    
    <!-- 云朵和烟雾 -->
    <g opacity="0.4">
      <ellipse cx="400" cy="800" rx="100" ry="50" fill="#95a5a6"/>
      <ellipse cx="1520" cy="850" rx="120" ry="60" fill="#95a5a6"/>
      <ellipse cx="960" cy="900" rx="150" ry="70" fill="#bdc3c7"/>
    </g>
  </g>
  
  <!-- DAY 4 标识 -->
  <g transform="translate(960, 350)">
    <!-- 背景圆形装饰 -->
    <circle cx="0" cy="0" r="150" fill="#e74c3c" opacity="0.1"/>
    <circle cx="0" cy="0" r="120" fill="#c0392b" opacity="0.1"/>
    
    <!-- DAY 4 文字 -->
    <text x="0" y="20" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="120" font-weight="bold" fill="#e74c3c">
      DAY 4
    </text>
  </g>
  
  <!-- 主标题 -->
  <g transform="translate(960, 550)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
      转化与升华：用"收场"促成行动，用"说销"完成闭环
    </text>
  </g>
  
  <!-- 装饰性升华元素 -->
  <g opacity="0.2">
    <!-- 左侧上升箭头 -->
    <g transform="translate(200, 700)">
      <path d="M 0 50 L 0 -50 M -15 -35 L 0 -50 L 15 -35" stroke="#e74c3c" stroke-width="6" fill="none"/>
      <circle cx="0" cy="50" r="8" fill="#e74c3c"/>
    </g>
    
    <!-- 右侧上升箭头 -->
    <g transform="translate(1720, 650)">
      <path d="M 0 40 L 0 -40 M -12 -25 L 0 -40 L 12 -25" stroke="#3498db" stroke-width="5" fill="none"/>
      <circle cx="0" cy="40" r="6" fill="#3498db"/>
    </g>
  </g>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.4">
    <path d="M 200 800 Q 960 700 1720 800" stroke="#e74c3c" stroke-width="8" fill="none"/>
    <path d="M 300 850 Q 960 750 1620 850" stroke="#f39c12" stroke-width="6" fill="none"/>
    <path d="M 400 900 Q 960 800 1520 900" stroke="#f1c40f" stroke-width="4" fill="none"/>
  </g>
  
  <!-- 底部装饰线条 -->
  <g opacity="0.3">
    <rect x="0" y="950" width="1920" height="4" fill="#e74c3c"/>
    <rect x="0" y="970" width="1920" height="3" fill="#f39c12"/>
    <rect x="0" y="985" width="1920" height="2" fill="#f1c40f"/>
  </g>
</svg>
