<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <g transform="translate(960, 150)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
      工作坊：解决方案框架设计
    </text>
  </g>
  
  <!-- 主视觉：高信任度框架设计模板 -->
  <g transform="translate(960, 450)">
    <!-- 模板背景 -->
    <rect x="-400" y="-200" width="800" height="400" fill="#f8f9fa" rx="20"/>
    <rect x="-380" y="-180" width="760" height="360" fill="#e9ecef" rx="15"/>
    
    <!-- 模板标题 -->
    <text x="0" y="-140" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" font-weight="bold" fill="#2c3e50">
      《高信任度框架设计模板》
    </text>
    
    <!-- 模板结构示意 -->
    <g transform="translate(0, -50)">
      <!-- 步骤1 -->
      <g transform="translate(-250, -50)">
        <rect x="-60" y="-20" width="120" height="40" fill="#3498db" opacity="0.2" rx="8"/>
        <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#3498db">
          1. 锁定钉子
        </text>
      </g>
      
      <!-- 步骤2 -->
      <g transform="translate(-80, -50)">
        <rect x="-60" y="-20" width="120" height="40" fill="#27ae60" opacity="0.2" rx="8"/>
        <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#27ae60">
          2. 选择结构
        </text>
      </g>
      
      <!-- 步骤3 -->
      <g transform="translate(90, -50)">
        <rect x="-60" y="-20" width="120" height="40" fill="#f39c12" opacity="0.2" rx="8"/>
        <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#f39c12">
          3. 拆解步骤
        </text>
      </g>
      
      <!-- 步骤4 -->
      <g transform="translate(260, -50)">
        <rect x="-60" y="-20" width="120" height="40" fill="#e74c3c" opacity="0.2" rx="8"/>
        <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#e74c3c">
          4. 质检标准
        </text>
      </g>
      
      <!-- 连接线 -->
      <g opacity="0.4">
        <path d="M -190 -50 L -140 -50" stroke="#7f8c8d" stroke-width="2" marker-end="url(#arrowhead)"/>
        <path d="M -20 -50 L 30 -50" stroke="#7f8c8d" stroke-width="2" marker-end="url(#arrowhead)"/>
        <path d="M 150 -50 L 200 -50" stroke="#7f8c8d" stroke-width="2" marker-end="url(#arrowhead)"/>
      </g>
      
      <!-- 中央框架图 -->
      <g transform="translate(0, 50)">
        <rect x="-150" y="-60" width="300" height="120" fill="#ffffff" rx="10"/>
        <rect x="-140" y="-50" width="280" height="100" fill="#f8f9fa" rx="8"/>
        
        <!-- 框架网格 -->
        <g opacity="0.3">
          <line x1="-120" y1="-30" x2="120" y2="-30" stroke="#7f8c8d" stroke-width="1"/>
          <line x1="-120" y1="0" x2="120" y2="0" stroke="#7f8c8d" stroke-width="1"/>
          <line x1="-120" y1="30" x2="120" y2="30" stroke="#7f8c8d" stroke-width="1"/>
          <line x1="-80" y1="-40" x2="-80" y2="40" stroke="#7f8c8d" stroke-width="1"/>
          <line x1="0" y1="-40" x2="0" y2="40" stroke="#7f8c8d" stroke-width="1"/>
          <line x1="80" y1="-40" x2="80" y2="40" stroke="#7f8c8d" stroke-width="1"/>
        </g>
        
        <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="16" fill="#7f8c8d">
          框架设计区域
        </text>
      </g>
    </g>
  </g>
  
  <!-- 操作指引 -->
  <g transform="translate(960, 750)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="32" fill="#2c3e50">
      请翻到学员手册第Z页。
    </text>
    
    <text x="0" y="50" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" fill="#7f8c8d">
      为你的"钉子"，绘制一张"解决方案地图"。
    </text>
    
    <!-- 时间提示 -->
    <g transform="translate(0, 120)">
      <circle cx="0" cy="0" r="50" fill="#e74c3c" opacity="0.1"/>
      <text x="0" y="10" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="48" font-weight="bold" fill="#e74c3c">
        40:00
      </text>
    </g>
  </g>
  
  <!-- 装饰性设计工具 -->
  <g opacity="0.2">
    <!-- 左侧铅笔 -->
    <g transform="translate(200, 400)">
      <rect x="-5" y="-80" width="10" height="160" fill="#f39c12" rx="2"/>
      <path d="M -8 -80 L 8 -80 L 0 -95 Z" fill="#e67e22"/>
      <circle cx="0" cy="80" r="8" fill="#34495e"/>
      <circle cx="0" cy="80" r="5" fill="#2c3e50"/>
    </g>
    
    <!-- 右侧圆规 -->
    <g transform="translate(1720, 500)">
      <line x1="-30" y1="0" x2="30" y2="0" stroke="#3498db" stroke-width="3"/>
      <circle cx="-30" cy="0" r="5" fill="#2980b9"/>
      <circle cx="30" cy="0" r="3" fill="#2980b9"/>
      <line x1="0" y1="-40" x2="0" y2="0" stroke="#3498db" stroke-width="2"/>
    </g>
    
    <!-- 底部直尺 -->
    <g transform="translate(960, 950)">
      <rect x="-200" y="-5" width="400" height="10" fill="#95a5a6" rx="5"/>
      <!-- 刻度 -->
      <line x1="-150" y1="-8" x2="-150" y2="8" stroke="#7f8c8d" stroke-width="1"/>
      <line x1="-100" y1="-8" x2="-100" y2="8" stroke="#7f8c8d" stroke-width="1"/>
      <line x1="-50" y1="-8" x2="-50" y2="8" stroke="#7f8c8d" stroke-width="1"/>
      <line x1="0" y1="-8" x2="0" y2="8" stroke="#7f8c8d" stroke-width="1"/>
      <line x1="50" y1="-8" x2="50" y2="8" stroke="#7f8c8d" stroke-width="1"/>
      <line x1="100" y1="-8" x2="100" y2="8" stroke="#7f8c8d" stroke-width="1"/>
      <line x1="150" y1="-8" x2="150" y2="8" stroke="#7f8c8d" stroke-width="1"/>
    </g>
  </g>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#7f8c8d"/>
    </marker>
  </defs>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.3">
    <path d="M 100 1000 Q 960 950 1820 1000" stroke="#3498db" stroke-width="4" fill="none"/>
  </g>
</svg>
