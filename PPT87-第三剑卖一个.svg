<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <g transform="translate(960, 120)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
      第三剑：卖一个
    </text>
  </g>
  
  <!-- 主视觉：道路与聚光灯 -->
  <g transform="translate(960, 450)">
    <!-- 道路背景 -->
    <g opacity="0.3">
      <rect x="-400" y="50" width="800" height="200" fill="#7f8c8d"/>
      <rect x="-380" y="70" width="760" height="160" fill="#95a5a6"/>
      
      <!-- 道路分割线 -->
      <rect x="-10" y="70" width="20" height="160" fill="#ffffff"/>
    </g>
    
    <!-- 多条岔路（暗淡） -->
    <g opacity="0.4">
      <!-- 岔路1 -->
      <path d="M -200 150 L -350 50 L -320 30 L -170 130 Z" fill="#bdc3c7"/>
      <text x="-260" y="90" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#7f8c8d">
        产品A
      </text>
      
      <!-- 岔路2 -->
      <path d="M -100 150 L -250 0 L -220 -20 L -70 130 Z" fill="#bdc3c7"/>
      <text x="-160" y="65" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#7f8c8d">
        产品B
      </text>
      
      <!-- 岔路3 -->
      <path d="M 100 150 L 250 0 L 280 20 L 130 170 Z" fill="#bdc3c7"/>
      <text x="190" y="85" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#7f8c8d">
        产品C
      </text>
      
      <!-- 岔路4 -->
      <path d="M 200 150 L 350 50 L 380 70 L 230 170 Z" fill="#bdc3c7"/>
      <text x="290" y="110" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#7f8c8d">
        产品D
      </text>
    </g>
    
    <!-- 主道路（被聚光灯照亮） -->
    <g>
      <rect x="-100" y="50" width="200" height="200" fill="#3498db"/>
      <rect x="-80" y="70" width="160" height="160" fill="#2980b9"/>
      
      <!-- 主道路标识 -->
      <text x="0" y="160" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" font-weight="bold" fill="#ffffff">
        核心产品
      </text>
    </g>
    
    <!-- 聚光灯效果 -->
    <g transform="translate(0, -200)">
      <!-- 聚光灯主体 -->
      <ellipse cx="0" cy="0" rx="40" ry="20" fill="#f1c40f"/>
      <ellipse cx="0" cy="0" rx="30" ry="15" fill="#f39c12"/>
      
      <!-- 光束 -->
      <path d="M -30 20 L -100 250 L 100 250 L 30 20 Z" fill="#f1c40f" opacity="0.6"/>
      <path d="M -25 20 L -80 230 L 80 230 L 25 20 Z" fill="#f39c12" opacity="0.8"/>
      
      <!-- 光线 -->
      <g opacity="0.4">
        <line x1="-20" y1="20" x2="-60" y2="200" stroke="#ffffff" stroke-width="2"/>
        <line x1="0" y1="20" x2="0" y2="220" stroke="#ffffff" stroke-width="3"/>
        <line x1="20" y1="20" x2="60" y2="200" stroke="#ffffff" stroke-width="2"/>
      </g>
    </g>
    
    <!-- 终点标识 -->
    <g transform="translate(0, 300)">
      <circle cx="0" cy="0" r="30" fill="#27ae60"/>
      <circle cx="0" cy="0" r="20" fill="#2ecc71"/>
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="16" font-weight="bold" fill="#ffffff">
        成功
      </text>
    </g>
  </g>
  
  <!-- 核心理念 -->
  <g transform="translate(960, 700)">
    <rect x="-450" y="-30" width="900" height="60" fill="#ebf3fd" rx="30"/>
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" font-weight="bold" fill="#3498db">
      只主推一个核心产品，让客户的决策成本降到最低。
    </text>
  </g>
  
  <!-- 原则说明 -->
  <g transform="translate(960, 800)">
    <rect x="-400" y="-30" width="800" height="60" fill="#3498db" rx="30"/>
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" font-weight="bold" fill="#ffffff">
      不要让用户做选择题，只让他做是非题。
    </text>
  </g>
  
  <!-- 装饰性选择元素 -->
  <g opacity="0.2">
    <!-- 左侧多选框（划掉） -->
    <g transform="translate(200, 500)">
      <rect x="-30" y="-20" width="60" height="40" fill="#e74c3c" rx="5"/>
      <rect x="-25" y="-15" width="50" height="30" fill="#c0392b" rx="3"/>
      
      <!-- 多个选项 -->
      <rect x="-20" y="-10" width="8" height="8" fill="#ffffff"/>
      <rect x="-20" y="0" width="8" height="8" fill="#ffffff"/>
      <rect x="-20" y="10" width="8" height="8" fill="#ffffff"/>
      
      <!-- 划掉的线 -->
      <line x1="-35" y1="-25" x2="35" y2="25" stroke="#e74c3c" stroke-width="4"/>
    </g>
    
    <!-- 右侧单选框（勾选） -->
    <g transform="translate(1720, 450)">
      <rect x="-20" y="-15" width="40" height="30" fill="#27ae60" rx="5"/>
      <rect x="-15" y="-10" width="30" height="20" fill="#2ecc71" rx="3"/>
      
      <!-- 单个选项 -->
      <rect x="-10" y="-5" width="10" height="10" fill="#ffffff"/>
      <path d="M -7 0 L -4 3 L -1 -3" stroke="#27ae60" stroke-width="2" fill="none"/>
    </g>
  </g>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.3">
    <path d="M 100 950 Q 960 900 1820 950" stroke="#3498db" stroke-width="4" fill="none"/>
  </g>
</svg>
