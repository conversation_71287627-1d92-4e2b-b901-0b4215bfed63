<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <g transform="translate(960, 120)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
      第二板斧：放大渴望
    </text>
  </g>
  
  <!-- 主视觉：从黑暗洞穴望向光明 -->
  <g transform="translate(960, 400)">
    <!-- 洞穴背景 -->
    <g>
      <!-- 洞穴内部（黑暗） -->
      <ellipse cx="0" cy="0" rx="400" ry="200" fill="#2c3e50"/>
      <ellipse cx="0" cy="0" rx="380" ry="180" fill="#34495e"/>
      
      <!-- 洞穴口（光明） -->
      <ellipse cx="300" cy="-50" rx="150" ry="100" fill="#f1c40f" opacity="0.8"/>
      <ellipse cx="300" cy="-50" rx="120" ry="80" fill="#f39c12"/>
      <ellipse cx="300" cy="-50" rx="90" ry="60" fill="#ffffff" opacity="0.9"/>
      
      <!-- 光线射入 -->
      <g opacity="0.6">
        <path d="M 150 -100 L -200 -150 L -180 -130 Z" fill="#f1c40f"/>
        <path d="M 200 -80 L -150 -100 L -130 -80 Z" fill="#f39c12"/>
        <path d="M 250 -60 L -100 -50 L -80 -30 Z" fill="#f1c40f"/>
        <path d="M 180 0 L -200 50 L -180 70 Z" fill="#f39c12"/>
        <path d="M 220 20 L -150 100 L -130 120 Z" fill="#f1c40f"/>
      </g>
      
      <!-- 洞穴岩石纹理 -->
      <g opacity="0.3">
        <circle cx="-300" cy="-80" r="20" fill="#7f8c8d"/>
        <circle cx="-250" cy="60" r="15" fill="#95a5a6"/>
        <circle cx="-350" cy="20" r="18" fill="#7f8c8d"/>
        <circle cx="100" cy="-120" r="12" fill="#95a5a6"/>
        <circle cx="50" cy="80" r="16" fill="#7f8c8d"/>
      </g>
    </g>
    
    <!-- 人影剪影（第一视角暗示） -->
    <g transform="translate(-200, 50)" opacity="0.4">
      <circle cx="0" cy="-30" r="15" fill="#2c3e50"/>
      <rect x="-10" y="-15" width="20" height="30" fill="#2c3e50" rx="3"/>
      <rect x="-15" y="15" width="10" height="25" fill="#2c3e50"/>
      <rect x="5" y="15" width="10" height="25" fill="#2c3e50"/>
    </g>
  </g>
  
  <!-- 核心要点 -->
  <g transform="translate(960, 650)">
    <!-- 目标 -->
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" font-weight="bold" fill="#f39c12">
      目标：在他痛苦的现状和理想的未来之间，拉出巨大的势能差。
    </text>
    
    <!-- 技巧框 -->
    <g transform="translate(0, 80)">
      <rect x="-250" y="-40" width="500" height="100" fill="#fef9e7" rx="15"/>
      <rect x="-230" y="-20" width="460" height="60" fill="#fdeaa7" opacity="0.5" rx="10"/>
      
      <text x="0" y="-30" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" font-weight="bold" fill="#2c3e50">
        技巧：
      </text>
      
      <g transform="translate(-80, 0)">
        <circle cx="0" cy="0" r="4" fill="#e67e22"/>
        <text x="15" y="5" font-family="Microsoft YaHei, sans-serif" font-size="20" fill="#2c3e50">
          重新定义
        </text>
      </g>
      
      <g transform="translate(80, 0)">
        <circle cx="0" cy="0" r="4" fill="#d35400"/>
        <text x="15" y="5" font-family="Microsoft YaHei, sans-serif" font-size="20" fill="#2c3e50">
          愿景描绘
        </text>
      </g>
    </g>
  </g>
  
  <!-- 感受标签 -->
  <g transform="translate(960, 850)">
    <rect x="-150" y="-30" width="300" height="60" fill="#f39c12" rx="30"/>
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" font-weight="bold" fill="#ffffff">
      让他产生："我想要！"的冲动
    </text>
  </g>
  
  <!-- 装饰性光芒元素 -->
  <g opacity="0.3">
    <!-- 左侧光芒 -->
    <g transform="translate(200, 200)">
      <path d="M 0 0 L 20 -40 L 40 0 L 20 40 Z" fill="#f1c40f"/>
      <path d="M 0 0 L 40 -20 L 80 0 L 40 20 Z" fill="#f39c12"/>
    </g>
    
    <!-- 右侧光芒 -->
    <g transform="translate(1720, 300)">
      <path d="M 0 0 L 15 -30 L 30 0 L 15 30 Z" fill="#f1c40f"/>
      <path d="M 0 0 L 30 -15 L 60 0 L 30 15 Z" fill="#f39c12"/>
    </g>
    
    <!-- 底部光芒 -->
    <g transform="translate(960, 950)">
      <path d="M 0 0 L 25 -50 L 50 0 L 25 50 Z" fill="#f1c40f"/>
      <path d="M 0 0 L 50 -25 L 100 0 L 50 25 Z" fill="#f39c12"/>
    </g>
  </g>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.3">
    <path d="M 100 1000 Q 960 950 1820 1000" stroke="#f39c12" stroke-width="4" fill="none"/>
  </g>
</svg>
