<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 舞台背景 -->
  <g opacity="0.1">
    <rect x="0" y="600" width="1920" height="480" fill="#2c3e50"/>
    <!-- 舞台纹理 -->
    <rect x="0" y="620" width="1920" height="20" fill="#34495e"/>
    <rect x="0" y="660" width="1920" height="15" fill="#34495e"/>
    <rect x="0" y="700" width="1920" height="10" fill="#34495e"/>
  </g>
  
  <!-- 标题 -->
  <g transform="translate(960, 150)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
      实战演练："钉子"发布会
    </text>
  </g>
  
  <!-- 聚光灯和麦克风 -->
  <g transform="translate(960, 400)">
    <!-- 聚光灯光束 -->
    <defs>
      <radialGradient id="spotlight" cx="50%" cy="0%" r="50%">
        <stop offset="0%" style="stop-color:#f1c40f;stop-opacity:0.8"/>
        <stop offset="50%" style="stop-color:#f39c12;stop-opacity:0.4"/>
        <stop offset="100%" style="stop-color:#e67e22;stop-opacity:0.1"/>
      </radialGradient>
    </defs>
    
    <ellipse cx="0" cy="100" rx="200" ry="300" fill="url(#spotlight)"/>
    
    <!-- 麦克风 -->
    <g transform="translate(0, 50)">
      <!-- 麦克风头 -->
      <ellipse cx="0" cy="-30" rx="25" ry="35" fill="#34495e"/>
      <ellipse cx="0" cy="-30" rx="20" ry="30" fill="#2c3e50"/>
      
      <!-- 麦克风杆 -->
      <rect x="-3" y="5" width="6" height="80" fill="#7f8c8d" rx="3"/>
      
      <!-- 麦克风底座 -->
      <ellipse cx="0" cy="100" rx="40" ry="15" fill="#34495e"/>
      <ellipse cx="0" cy="95" rx="35" ry="12" fill="#2c3e50"/>
      
      <!-- 音波效果 -->
      <g opacity="0.6">
        <circle cx="0" cy="-30" r="50" fill="none" stroke="#e74c3c" stroke-width="2"/>
        <circle cx="0" cy="-30" r="70" fill="none" stroke="#e74c3c" stroke-width="1.5"/>
        <circle cx="0" cy="-30" r="90" fill="none" stroke="#e74c3c" stroke-width="1"/>
      </g>
    </g>
  </g>
  
  <!-- 目标和规则 -->
  <g transform="translate(960, 650)">
    <!-- 目标 -->
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="36" font-weight="bold" fill="#e74c3c">
      目标：在60秒内，让我们所有人都想报名你的课程！
    </text>
    
    <!-- 规则框 -->
    <g transform="translate(0, 80)">
      <rect x="-400" y="-40" width="800" height="120" fill="#ecf0f1" rx="15"/>
      <rect x="-380" y="-20" width="760" height="80" fill="#bdc3c7" opacity="0.3" rx="10"/>
      
      <text x="0" y="-30" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" font-weight="bold" fill="#2c3e50">
        规则：
      </text>
      
      <g transform="translate(-200, 0)">
        <circle cx="0" cy="0" r="4" fill="#3498db"/>
        <text x="15" y="5" font-family="Microsoft YaHei, sans-serif" font-size="22" fill="#2c3e50">
          小组推选一位代表。
        </text>
      </g>
      
      <g transform="translate(-200, 30)">
        <circle cx="0" cy="0" r="4" fill="#f39c12"/>
        <text x="15" y="5" font-family="Microsoft YaHei, sans-serif" font-size="22" fill="#2c3e50">
          严格遵守60秒时间。
        </text>
      </g>
      
      <g transform="translate(100, 15)">
        <circle cx="0" cy="0" r="4" fill="#27ae60"/>
        <text x="15" y="5" font-family="Microsoft YaHei, sans-serif" font-size="22" fill="#2c3e50">
          接受全场"投资人"的检验。
        </text>
      </g>
    </g>
  </g>
  
  <!-- 装饰性舞台灯光 -->
  <g opacity="0.3">
    <!-- 左侧灯光 -->
    <g transform="translate(200, 200)">
      <circle cx="0" cy="0" r="20" fill="#f1c40f"/>
      <path d="M 0 20 L -50 200 L 50 200 Z" fill="#f39c12" opacity="0.4"/>
    </g>
    
    <!-- 右侧灯光 -->
    <g transform="translate(1720, 200)">
      <circle cx="0" cy="0" r="20" fill="#e74c3c"/>
      <path d="M 0 20 L -50 200 L 50 200 Z" fill="#c0392b" opacity="0.4"/>
    </g>
    
    <!-- 顶部灯光 -->
    <g transform="translate(960, 100)">
      <circle cx="0" cy="0" r="15" fill="#3498db"/>
      <ellipse cx="0" cy="100" rx="100" ry="50" fill="#2980b9" opacity="0.2"/>
    </g>
  </g>
  
  <!-- 观众席暗示 -->
  <g opacity="0.2">
    <g transform="translate(300, 800)">
      <circle cx="0" cy="0" r="8" fill="#34495e"/>
      <rect x="-6" y="8" width="12" height="20" fill="#34495e"/>
    </g>
    
    <g transform="translate(400, 820)">
      <circle cx="0" cy="0" r="8" fill="#34495e"/>
      <rect x="-6" y="8" width="12" height="20" fill="#34495e"/>
    </g>
    
    <g transform="translate(500, 810)">
      <circle cx="0" cy="0" r="8" fill="#34495e"/>
      <rect x="-6" y="8" width="12" height="20" fill="#34495e"/>
    </g>
    
    <g transform="translate(1420, 800)">
      <circle cx="0" cy="0" r="8" fill="#34495e"/>
      <rect x="-6" y="8" width="12" height="20" fill="#34495e"/>
    </g>
    
    <g transform="translate(1520, 820)">
      <circle cx="0" cy="0" r="8" fill="#34495e"/>
      <rect x="-6" y="8" width="12" height="20" fill="#34495e"/>
    </g>
    
    <g transform="translate(1620, 810)">
      <circle cx="0" cy="0" r="8" fill="#34495e"/>
      <rect x="-6" y="8" width="12" height="20" fill="#34495e"/>
    </g>
  </g>
  
  <!-- 时间倒计时装饰 -->
  <g transform="translate(1600, 300)" opacity="0.4">
    <circle cx="0" cy="0" r="40" fill="#e74c3c" opacity="0.2"/>
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" font-weight="bold" fill="#e74c3c">
      60s
    </text>
  </g>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.3">
    <path d="M 100 950 Q 960 900 1820 950" stroke="#f39c12" stroke-width="4" fill="none"/>
  </g>
</svg>
