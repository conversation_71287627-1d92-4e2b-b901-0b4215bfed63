<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <g transform="translate(960, 100)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
      案例拆解：从1.0到2.0的认知升维
    </text>
  </g>
  
  <!-- 左侧：课程1.0 -->
  <g transform="translate(480, 540)">
    <!-- 背景区域 -->
    <rect x="-400" y="-350" width="800" height="700" fill="#ecf0f1" rx="20"/>
    <rect x="-380" y="-330" width="760" height="660" fill="#bdc3c7" opacity="0.3" rx="15"/>
    
    <!-- 标题 -->
    <text x="0" y="-280" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="32" font-weight="bold" fill="#2c3e50">
      课程1.0
    </text>
    
    <!-- 课程名称 -->
    <text x="0" y="-230" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" font-weight="bold" fill="#e74c3c">
      《高效能人士的时间管理二十法》
    </text>
    
    <!-- 模式标识 -->
    <g transform="translate(0, -180)">
      <rect x="-60" y="-15" width="120" height="30" fill="#7f8c8d" rx="15"/>
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="16" font-weight="bold" fill="#ffffff">
        纪录片模式
      </text>
    </g>
    
    <!-- 大纲要点 -->
    <text x="0" y="-120" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="20" font-weight="bold" fill="#34495e">
      大纲要点：
    </text>
    
    <g transform="translate(-150, -80)">
      <circle cx="0" cy="0" r="4" fill="#7f8c8d"/>
      <text x="15" y="5" font-family="Microsoft YaHei, sans-serif" font-size="18" fill="#2c3e50">
        认识时间
      </text>
    </g>
    
    <g transform="translate(-150, -40)">
      <circle cx="0" cy="0" r="4" fill="#7f8c8d"/>
      <text x="15" y="5" font-family="Microsoft YaHei, sans-serif" font-size="18" fill="#2c3e50">
        目标设定 (SMART)
      </text>
    </g>
    
    <g transform="translate(-150, 0)">
      <circle cx="0" cy="0" r="4" fill="#7f8c8d"/>
      <text x="15" y="5" font-family="Microsoft YaHei, sans-serif" font-size="18" fill="#2c3e50">
        计划制定
      </text>
    </g>
    
    <g transform="translate(-150, 40)">
      <circle cx="0" cy="0" r="4" fill="#7f8c8d"/>
      <text x="15" y="5" font-family="Microsoft YaHei, sans-serif" font-size="18" fill="#2c3e50">
        经典方法 (四象限, GTD...)
      </text>
    </g>
    
    <g transform="translate(-150, 80)">
      <circle cx="0" cy="0" r="4" fill="#7f8c8d"/>
      <text x="15" y="5" font-family="Microsoft YaHei, sans-serif" font-size="18" fill="#2c3e50">
        实用工具
      </text>
    </g>
    
    <!-- 特征标签 -->
    <g transform="translate(0, 200)">
      <rect x="-100" y="-20" width="200" height="40" fill="#95a5a6" rx="20"/>
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="16" fill="#ffffff">
        全面 · 系统 · 干货满满
      </text>
    </g>
  </g>
  
  <!-- 右侧：课程2.0 -->
  <g transform="translate(1440, 540)">
    <!-- 背景区域 -->
    <rect x="-400" y="-350" width="800" height="700" fill="#e8f8f5" rx="20"/>
    <rect x="-380" y="-330" width="760" height="660" fill="#27ae60" opacity="0.1" rx="15"/>
    
    <!-- 标题 -->
    <text x="0" y="-280" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="32" font-weight="bold" fill="#2c3e50">
      课程2.0
    </text>
    
    <!-- 课程名称 -->
    <text x="0" y="-230" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" font-weight="bold" fill="#27ae60">
      《告别瞎忙：顶级高手的精力管理系统》
    </text>
    
    <!-- 模式标识 -->
    <g transform="translate(0, -180)">
      <rect x="-60" y="-15" width="120" height="30" fill="#27ae60" rx="15"/>
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="16" font-weight="bold" fill="#ffffff">
        大片模式
      </text>
    </g>
    
    <!-- 英雄之旅逻辑 -->
    <text x="0" y="-120" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="20" font-weight="bold" fill="#34495e">
      英雄之旅逻辑：
    </text>
    
    <g transform="translate(-180, -80)">
      <rect x="0" y="-10" width="8" height="20" fill="#e74c3c" rx="2"/>
      <text x="20" y="5" font-family="Microsoft YaHei, sans-serif" font-size="18" fill="#2c3e50">
        <tspan font-weight="bold" fill="#e74c3c">危机：</tspan>为什么你越学时间管理，反而越焦虑？
      </text>
    </g>
    
    <g transform="translate(-180, -40)">
      <rect x="0" y="-10" width="8" height="20" fill="#f39c12" rx="2"/>
      <text x="20" y="5" font-family="Microsoft YaHei, sans-serif" font-size="18" fill="#2c3e50">
        <tspan font-weight="bold" fill="#f39c12">颠覆：</tspan>打碎"时间管理"旧神，建立"精力管理"新神。
      </text>
    </g>
    
    <g transform="translate(-180, 0)">
      <rect x="0" y="-10" width="8" height="20" fill="#3498db" rx="2"/>
      <text x="20" y="5" font-family="Microsoft YaHei, sans-serif" font-size="18" fill="#2c3e50">
        <tspan font-weight="bold" fill="#3498db">方案：</tspan>只给一套"精力充电"系统。
      </text>
    </g>
    
    <g transform="translate(-180, 40)">
      <rect x="0" y="-10" width="8" height="20" fill="#27ae60" rx="2"/>
      <text x="20" y="5" font-family="Microsoft YaHei, sans-serif" font-size="18" fill="#2c3e50">
        <tspan font-weight="bold" fill="#27ae60">结局：</tspan>成为精力主人，告别内耗。
      </text>
    </g>
    
    <!-- 特征标签 -->
    <g transform="translate(0, 200)">
      <rect x="-120" y="-20" width="240" height="40" fill="#27ae60" rx="20"/>
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="16" fill="#ffffff">
        颠覆 · 聚焦 · 转化力强
      </text>
    </g>
  </g>
  
  <!-- 中间箭头 -->
  <g transform="translate(960, 540)">
    <path d="M -80 0 L 80 0" stroke="#e74c3c" stroke-width="6" fill="none" marker-end="url(#arrowhead)"/>
    <text x="0" y="-20" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" font-weight="bold" fill="#e74c3c">
      升维
    </text>
  </g>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c"/>
    </marker>
  </defs>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.3">
    <path d="M 100 950 Q 960 900 1820 950" stroke="#27ae60" stroke-width="4" fill="none"/>
  </g>
</svg>
