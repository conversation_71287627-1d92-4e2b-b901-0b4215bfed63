<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <g transform="translate(960, 120)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
      第二剑：卖信任
    </text>
  </g>
  
  <!-- 主视觉：拼图组成品牌LOGO -->
  <g transform="translate(960, 400)">
    <!-- 中心品牌LOGO圆形 -->
    <circle cx="0" cy="0" r="100" fill="#27ae60"/>
    <circle cx="0" cy="0" r="80" fill="#2ecc71"/>
    <text x="0" y="10" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="32" font-weight="bold" fill="#ffffff">
      品牌
    </text>
    
    <!-- 拼图片1 - 左上 -->
    <g transform="translate(-120, -120)">
      <path d="M 0 0 L 60 0 Q 70 0 70 10 L 70 50 Q 70 60 60 60 L 10 60 Q 0 60 0 50 L 0 10 Q 0 0 10 0 Z" fill="#3498db"/>
      <path d="M 5 5 L 55 5 Q 65 5 65 15 L 65 45 Q 65 55 55 55 L 15 55 Q 5 55 5 45 L 5 15 Q 5 5 15 5 Z" fill="#2980b9"/>
      
      <!-- 客户笑脸 -->
      <circle cx="35" cy="30" r="20" fill="#f39c12"/>
      <circle cx="30" cy="25" r="2" fill="#2c3e50"/>
      <circle cx="40" cy="25" r="2" fill="#2c3e50"/>
      <path d="M 28 35 Q 35 40 42 35" stroke="#2c3e50" stroke-width="2" fill="none"/>
    </g>
    
    <!-- 拼图片2 - 右上 -->
    <g transform="translate(60, -120)">
      <path d="M 0 0 L 60 0 Q 70 0 70 10 L 70 50 Q 70 60 60 60 L 10 60 Q 0 60 0 50 L 0 10 Q 0 0 10 0 Z" fill="#e74c3c"/>
      <path d="M 5 5 L 55 5 Q 65 5 65 15 L 65 45 Q 65 55 55 55 L 15 55 Q 5 55 5 45 L 5 15 Q 5 5 15 5 Z" fill="#c0392b"/>
      
      <!-- 客户笑脸 -->
      <circle cx="35" cy="30" r="20" fill="#f1c40f"/>
      <circle cx="30" cy="25" r="2" fill="#2c3e50"/>
      <circle cx="40" cy="25" r="2" fill="#2c3e50"/>
      <path d="M 28 35 Q 35 40 42 35" stroke="#2c3e50" stroke-width="2" fill="none"/>
    </g>
    
    <!-- 拼图片3 - 左下 -->
    <g transform="translate(-120, 60)">
      <path d="M 0 0 L 60 0 Q 70 0 70 10 L 70 50 Q 70 60 60 60 L 10 60 Q 0 60 0 50 L 0 10 Q 0 0 10 0 Z" fill="#9b59b6"/>
      <path d="M 5 5 L 55 5 Q 65 5 65 15 L 65 45 Q 65 55 55 55 L 15 55 Q 5 55 5 45 L 5 15 Q 5 5 15 5 Z" fill="#8e44ad"/>
      
      <!-- 客户笑脸 -->
      <circle cx="35" cy="30" r="20" fill="#e67e22"/>
      <circle cx="30" cy="25" r="2" fill="#2c3e50"/>
      <circle cx="40" cy="25" r="2" fill="#2c3e50"/>
      <path d="M 28 35 Q 35 40 42 35" stroke="#2c3e50" stroke-width="2" fill="none"/>
    </g>
    
    <!-- 拼图片4 - 右下 -->
    <g transform="translate(60, 60)">
      <path d="M 0 0 L 60 0 Q 70 0 70 10 L 70 50 Q 70 60 60 60 L 10 60 Q 0 60 0 50 L 0 10 Q 0 0 10 0 Z" fill="#f39c12"/>
      <path d="M 5 5 L 55 5 Q 65 5 65 15 L 65 45 Q 65 55 55 55 L 15 55 Q 5 55 5 45 L 5 15 Q 5 5 15 5 Z" fill="#e67e22"/>
      
      <!-- 客户笑脸 -->
      <circle cx="35" cy="30" r="20" fill="#3498db"/>
      <circle cx="30" cy="25" r="2" fill="#2c3e50"/>
      <circle cx="40" cy="25" r="2" fill="#2c3e50"/>
      <path d="M 28 35 Q 35 40 42 35" stroke="#2c3e50" stroke-width="2" fill="none"/>
    </g>
    
    <!-- 连接线 -->
    <g opacity="0.6">
      <line x1="-90" y1="-90" x2="-50" y2="-50" stroke="#f39c12" stroke-width="3"/>
      <line x1="90" y1="-90" x2="50" y2="-50" stroke="#f39c12" stroke-width="3"/>
      <line x1="-90" y1="90" x2="-50" y2="50" stroke="#f39c12" stroke-width="3"/>
      <line x1="90" y1="90" x2="50" y2="50" stroke="#f39c12" stroke-width="3"/>
    </g>
  </g>
  
  <!-- 核心理念 -->
  <g transform="translate(960, 650)">
    <rect x="-450" y="-30" width="900" height="60" fill="#e8f5e8" rx="30"/>
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" font-weight="bold" fill="#27ae60">
      客户见证不是最后的PPT，而是贯穿始终的故事。
    </text>
  </g>
  
  <!-- 做法说明 -->
  <g transform="translate(960, 750)">
    <rect x="-500" y="-40" width="1000" height="80" fill="#27ae60" rx="20"/>
    <text x="0" y="-5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" font-weight="bold" fill="#ffffff">
      将客户成功案例，揉碎了，变成一个个故事，
    </text>
    <text x="0" y="25" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" font-weight="bold" fill="#ffffff">
      作为你讲解方法论的最佳佐证。
    </text>
  </g>
  
  <!-- 装饰性信任元素 -->
  <g opacity="0.2">
    <!-- 左侧证书 -->
    <g transform="translate(200, 500)">
      <rect x="-25" y="-20" width="50" height="40" fill="#f1c40f" rx="3"/>
      <rect x="-20" y="-15" width="40" height="30" fill="#f39c12" rx="2"/>
      
      <!-- 证书内容 -->
      <line x1="-15" y1="-8" x2="15" y2="-8" stroke="#ffffff" stroke-width="1"/>
      <line x1="-15" y1="-2" x2="15" y2="-2" stroke="#ffffff" stroke-width="1"/>
      <line x1="-15" y1="4" x2="10" y2="4" stroke="#ffffff" stroke-width="1"/>
      
      <!-- 印章 -->
      <circle cx="12" cy="8" r="4" fill="#e74c3c"/>
    </g>
    
    <!-- 右侧五星评价 -->
    <g transform="translate(1720, 450)">
      <g>
        <path d="M 0 -8 L 2 -2 L 8 -2 L 3 2 L 5 8 L 0 5 L -5 8 L -3 2 L -8 -2 L -2 -2 Z" fill="#f1c40f"/>
      </g>
      <g transform="translate(20, 0)">
        <path d="M 0 -8 L 2 -2 L 8 -2 L 3 2 L 5 8 L 0 5 L -5 8 L -3 2 L -8 -2 L -2 -2 Z" fill="#f1c40f"/>
      </g>
      <g transform="translate(40, 0)">
        <path d="M 0 -8 L 2 -2 L 8 -2 L 3 2 L 5 8 L 0 5 L -5 8 L -3 2 L -8 -2 L -2 -2 Z" fill="#f1c40f"/>
      </g>
      <g transform="translate(-20, 0)">
        <path d="M 0 -8 L 2 -2 L 8 -2 L 3 2 L 5 8 L 0 5 L -5 8 L -3 2 L -8 -2 L -2 -2 Z" fill="#f1c40f"/>
      </g>
      <g transform="translate(-40, 0)">
        <path d="M 0 -8 L 2 -2 L 8 -2 L 3 2 L 5 8 L 0 5 L -5 8 L -3 2 L -8 -2 L -2 -2 Z" fill="#f1c40f"/>
      </g>
    </g>
  </g>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.3">
    <path d="M 100 950 Q 960 900 1820 950" stroke="#27ae60" stroke-width="4" fill="none"/>
  </g>
</svg>
