<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <g transform="translate(960, 120)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
      "钉子"的黄金公式
    </text>
  </g>
  
  <!-- 化学公式样式的设计 -->
  <g transform="translate(960, 350)">
    <!-- 公式背景 -->
    <rect x="-500" y="-80" width="1000" height="160" fill="#ecf0f1" rx="20"/>
    <rect x="-480" y="-60" width="960" height="120" fill="#bdc3c7" opacity="0.3" rx="15"/>
    
    <!-- 公式内容 -->
    <!-- 左侧：用户的核心问题 -->
    <g transform="translate(-300, 0)">
      <rect x="-120" y="-40" width="240" height="80" fill="#e74c3c" rx="10"/>
      <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="20" font-weight="bold" fill="#ffffff">
        用户的核心问题
      </text>
      <text x="0" y="15" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="16" fill="#ffffff">
        (剧痛)
      </text>
    </g>
    
    <!-- 加号 -->
    <g transform="translate(-100, 0)">
      <circle cx="0" cy="0" r="25" fill="#f39c12"/>
      <text x="0" y="8" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="32" font-weight="bold" fill="#ffffff">
        +
      </text>
    </g>
    
    <!-- 右侧：你的底层逻辑 -->
    <g transform="translate(100, 0)">
      <rect x="-120" y="-40" width="240" height="80" fill="#3498db" rx="10"/>
      <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="20" font-weight="bold" fill="#ffffff">
        你的底层逻辑
      </text>
      <text x="0" y="15" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="16" fill="#ffffff">
        (独家药方)
      </text>
    </g>
    
    <!-- 箭头 -->
    <g transform="translate(300, 0)">
      <path d="M -30 0 L 30 0" stroke="#27ae60" stroke-width="6" fill="none" marker-end="url(#arrowhead)"/>
    </g>
    
    <!-- 结果：你的钉子 -->
    <g transform="translate(450, 0)">
      <rect x="-80" y="-40" width="160" height="80" fill="#27ae60" rx="10"/>
      <text x="0" y="-5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="20" font-weight="bold" fill="#ffffff">
        你的"钉子"
      </text>
    </g>
  </g>
  
  <!-- 要素解释 -->
  <g transform="translate(960, 600)">
    <!-- 核心问题解释 -->
    <g transform="translate(-400, 0)">
      <circle cx="0" cy="-50" r="40" fill="#e74c3c" opacity="0.2"/>
      <text x="0" y="-80" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" font-weight="bold" fill="#e74c3c">
        核心问题
      </text>
      <text x="0" y="-40" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="22" fill="#2c3e50">
        不是"需求"，而是"剧痛"。
      </text>
      
      <!-- 特征 -->
      <g transform="translate(0, 20)">
        <circle cx="-80" cy="0" r="4" fill="#e74c3c"/>
        <text x="-60" y="5" font-family="Microsoft YaHei, sans-serif" font-size="18" fill="#7f8c8d">
          痛感强
        </text>
        
        <circle cx="0" cy="0" r="4" fill="#e74c3c"/>
        <text x="20" y="5" font-family="Microsoft YaHei, sans-serif" font-size="18" fill="#7f8c8d">
          高频发
        </text>
        
        <circle cx="80" cy="0" r="4" fill="#e74c3c"/>
        <text x="100" y="5" font-family="Microsoft YaHei, sans-serif" font-size="18" fill="#7f8c8d">
          愿付费
        </text>
      </g>
    </g>
    
    <!-- 底层逻辑解释 -->
    <g transform="translate(400, 0)">
      <circle cx="0" cy="-50" r="40" fill="#3498db" opacity="0.2"/>
      <text x="0" y="-80" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" font-weight="bold" fill="#3498db">
        底层逻辑
      </text>
      <text x="0" y="-40" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="22" fill="#2c3e50">
        不是"常识"，而是"独家药方"。
      </text>
      
      <!-- 特征 -->
      <g transform="translate(0, 20)">
        <circle cx="-80" cy="0" r="4" fill="#3498db"/>
        <text x="-60" y="5" font-family="Microsoft YaHei, sans-serif" font-size="18" fill="#7f8c8d">
          颠覆性
        </text>
        
        <circle cx="0" cy="0" r="4" fill="#3498db"/>
        <text x="20" y="5" font-family="Microsoft YaHei, sans-serif" font-size="18" fill="#7f8c8d">
          简单化
        </text>
        
        <circle cx="80" cy="0" r="4" fill="#3498db"/>
        <text x="100" y="5" font-family="Microsoft YaHei, sans-serif" font-size="18" fill="#7f8c8d">
          可复制
        </text>
      </g>
    </g>
  </g>
  
  <!-- 装饰性化学元素 -->
  <g opacity="0.2">
    <g transform="translate(200, 200)">
      <circle cx="0" cy="0" r="30" fill="#e74c3c"/>
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="20" font-weight="bold" fill="#ffffff">
        P
      </text>
      <text x="0" y="60" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="12" fill="#7f8c8d">
        Problem
      </text>
    </g>
    
    <g transform="translate(1720, 800)">
      <circle cx="0" cy="0" r="30" fill="#3498db"/>
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="20" font-weight="bold" fill="#ffffff">
        L
      </text>
      <text x="0" y="60" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="12" fill="#7f8c8d">
        Logic
      </text>
    </g>
  </g>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#27ae60"/>
    </marker>
  </defs>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.3">
    <path d="M 100 950 Q 960 900 1820 950" stroke="#27ae60" stroke-width="4" fill="none"/>
  </g>
</svg>
