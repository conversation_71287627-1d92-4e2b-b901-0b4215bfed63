<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <g transform="translate(960, 120)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
      第五剑：提前卖
    </text>
  </g>
  
  <!-- 主视觉：电影海报与预告片 -->
  <g transform="translate(960, 400)">
    <!-- 主海报 -->
    <g transform="translate(0, 0)">
      <rect x="-150" y="-200" width="300" height="400" fill="#2c3e50" rx="15"/>
      <rect x="-140" y="-190" width="280" height="380" fill="#34495e" rx="12"/>
      <rect x="-130" y="-180" width="260" height="360" fill="#ecf0f1" rx="10"/>
      
      <!-- 海报内容 -->
      <rect x="-120" y="-170" width="240" height="150" fill="#3498db" rx="8"/>
      <text x="0" y="-90" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" font-weight="bold" fill="#ffffff">
        高阶课程
      </text>
      
      <!-- 即将上映标识 -->
      <rect x="-100" y="-10" width="200" height="40" fill="#e74c3c" rx="20"/>
      <text x="0" y="15" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="20" font-weight="bold" fill="#ffffff">
        即将上映
      </text>
      
      <!-- 海报底部信息 -->
      <g transform="translate(0, 80)">
        <line x1="-100" y1="0" x2="100" y2="0" stroke="#bdc3c7" stroke-width="2"/>
        <line x1="-100" y1="20" x2="100" y2="20" stroke="#bdc3c7" stroke-width="2"/>
        <line x1="-100" y1="40" x2="80" y2="40" stroke="#bdc3c7" stroke-width="2"/>
        <line x1="-100" y1="60" x2="90" y2="60" stroke="#bdc3c7" stroke-width="2"/>
      </g>
    </g>
    
    <!-- 预告片小窗口1 -->
    <g transform="translate(-300, -100)">
      <rect x="-60" y="-40" width="120" height="80" fill="#f39c12" rx="8"/>
      <rect x="-50" y="-30" width="100" height="60" fill="#e67e22" rx="5"/>
      
      <!-- 播放按钮 -->
      <g transform="translate(0, 0)">
        <circle cx="0" cy="0" r="15" fill="#ffffff" opacity="0.8"/>
        <path d="M -5 -8 L -5 8 L 8 0 Z" fill="#f39c12"/>
      </g>
      
      <text x="0" y="60" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#f39c12">
        精彩预告1
      </text>
    </g>
    
    <!-- 预告片小窗口2 -->
    <g transform="translate(300, -50)">
      <rect x="-60" y="-40" width="120" height="80" fill="#27ae60" rx="8"/>
      <rect x="-50" y="-30" width="100" height="60" fill="#2ecc71" rx="5"/>
      
      <!-- 播放按钮 -->
      <g transform="translate(0, 0)">
        <circle cx="0" cy="0" r="15" fill="#ffffff" opacity="0.8"/>
        <path d="M -5 -8 L -5 8 L 8 0 Z" fill="#27ae60"/>
      </g>
      
      <text x="0" y="60" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#27ae60">
        精彩预告2
      </text>
    </g>
    
    <!-- 预告片小窗口3 -->
    <g transform="translate(-200, 150)">
      <rect x="-60" y="-40" width="120" height="80" fill="#9b59b6" rx="8"/>
      <rect x="-50" y="-30" width="100" height="60" fill="#8e44ad" rx="5"/>
      
      <!-- 播放按钮 -->
      <g transform="translate(0, 0)">
        <circle cx="0" cy="0" r="15" fill="#ffffff" opacity="0.8"/>
        <path d="M -5 -8 L -5 8 L 8 0 Z" fill="#9b59b6"/>
      </g>
      
      <text x="0" y="60" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#9b59b6">
        精彩预告3
      </text>
    </g>
    
    <!-- 连接线 -->
    <g opacity="0.6">
      <path d="M -240 -60 L -120 -120" stroke="#f39c12" stroke-width="3" stroke-dasharray="5,5"/>
      <path d="M 240 -10 L 120 -80" stroke="#27ae60" stroke-width="3" stroke-dasharray="5,5"/>
      <path d="M -140 110 L -80 60" stroke="#9b59b6" stroke-width="3" stroke-dasharray="5,5"/>
    </g>
  </g>
  
  <!-- 核心理念 -->
  <g transform="translate(960, 700)">
    <rect x="-450" y="-30" width="900" height="60" fill="#f4f3ff" rx="30"/>
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" font-weight="bold" fill="#9b59b6">
      从开场第一分钟，就在"卖"你的解决方案的价值。
    </text>
  </g>
  
  <!-- 话术技巧 -->
  <g transform="translate(960, 800)">
    <rect x="-550" y="-40" width="1100" height="80" fill="#9b59b6" rx="20"/>
    <text x="0" y="-5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="22" font-weight="bold" fill="#ffffff">
      "关于这一点，由于时间关系...在我最高阶的XX课程里，
    </text>
    <text x="0" y="25" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="22" font-weight="bold" fill="#ffffff">
      我们会用整整两天来解决..."
    </text>
  </g>
  
  <!-- 装饰性预告元素 -->
  <g opacity="0.2">
    <!-- 左侧胶片 -->
    <g transform="translate(200, 500)">
      <rect x="-15" y="-40" width="30" height="80" fill="#34495e" rx="3"/>
      <circle cx="-8" cy="-25" r="3" fill="#ecf0f1"/>
      <circle cx="8" cy="-25" r="3" fill="#ecf0f1"/>
      <circle cx="-8" cy="-5" r="3" fill="#ecf0f1"/>
      <circle cx="8" cy="-5" r="3" fill="#ecf0f1"/>
      <circle cx="-8" cy="15" r="3" fill="#ecf0f1"/>
      <circle cx="8" cy="15" r="3" fill="#ecf0f1"/>
      <circle cx="-8" cy="35" r="3" fill="#ecf0f1"/>
      <circle cx="8" cy="35" r="3" fill="#ecf0f1"/>
    </g>
    
    <!-- 右侧时钟 -->
    <g transform="translate(1720, 450)">
      <circle cx="0" cy="0" r="20" fill="#f39c12"/>
      <circle cx="0" cy="0" r="15" fill="#e67e22"/>
      
      <!-- 时钟指针 -->
      <line x1="0" y1="0" x2="0" y2="-10" stroke="#ffffff" stroke-width="2"/>
      <line x1="0" y1="0" x2="7" y2="0" stroke="#ffffff" stroke-width="2"/>
      <circle cx="0" cy="0" r="2" fill="#ffffff"/>
      
      <!-- 时钟刻度 -->
      <line x1="0" y1="-18" x2="0" y2="-15" stroke="#ffffff" stroke-width="1"/>
      <line x1="18" y1="0" x2="15" y2="0" stroke="#ffffff" stroke-width="1"/>
      <line x1="0" y1="18" x2="0" y2="15" stroke="#ffffff" stroke-width="1"/>
      <line x1="-18" y1="0" x2="-15" y2="0" stroke="#ffffff" stroke-width="1"/>
    </g>
  </g>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.3">
    <path d="M 100 950 Q 960 900 1820 950" stroke="#9b59b6" stroke-width="4" fill="none"/>
  </g>
</svg>
