<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <g transform="translate(960, 120)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
      工具："无冲突，不故事"五步创作法
    </text>
  </g>
  
  <!-- 电影场记板 -->
  <g transform="translate(960, 400)">
    <!-- 场记板主体 -->
    <rect x="-300" y="-200" width="600" height="400" fill="#2c3e50" rx="15"/>
    <rect x="-290" y="-190" width="580" height="380" fill="#34495e" rx="10"/>
    <rect x="-280" y="-180" width="560" height="360" fill="#ecf0f1" rx="8"/>
    
    <!-- 场记板顶部夹子 -->
    <rect x="-320" y="-220" width="640" height="40" fill="#2c3e50" rx="8"/>
    <rect x="-310" y="-210" width="620" height="20" fill="#34495e" rx="5"/>
    
    <!-- 黑白条纹 -->
    <g>
      <rect x="-310" y="-210" width="60" height="20" fill="#2c3e50"/>
      <rect x="-190" y="-210" width="60" height="20" fill="#2c3e50"/>
      <rect x="-70" y="-210" width="60" height="20" fill="#2c3e50"/>
      <rect x="50" y="-210" width="60" height="20" fill="#2c3e50"/>
      <rect x="170" y="-210" width="60" height="20" fill="#2c3e50"/>
    </g>
    
    <!-- 五个场景步骤 -->
    <g transform="translate(0, -100)">
      <!-- SCENE 1 -->
      <g transform="translate(-200, -50)">
        <rect x="-80" y="-30" width="160" height="60" fill="#3498db" rx="8"/>
        <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="16" font-weight="bold" fill="#ffffff">
          SCENE 1：背景
        </text>
        <text x="0" y="10" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#ffffff">
          (The Setup)
        </text>
      </g>
      
      <!-- SCENE 2 -->
      <g transform="translate(0, -50)">
        <rect x="-80" y="-30" width="160" height="60" fill="#e74c3c" rx="8"/>
        <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="16" font-weight="bold" fill="#ffffff">
          SCENE 2：冲突
        </text>
        <text x="0" y="10" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#ffffff">
          (The Conflict)
        </text>
      </g>
      
      <!-- SCENE 3 -->
      <g transform="translate(200, -50)">
        <rect x="-80" y="-30" width="160" height="60" fill="#f39c12" rx="8"/>
        <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="16" font-weight="bold" fill="#ffffff">
          SCENE 3：努力
        </text>
        <text x="0" y="10" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#ffffff">
          (The Struggle)
        </text>
      </g>
      
      <!-- SCENE 4 -->
      <g transform="translate(-100, 50)">
        <rect x="-80" y="-30" width="160" height="60" fill="#9b59b6" rx="8"/>
        <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="16" font-weight="bold" fill="#ffffff">
          SCENE 4：转折
        </text>
        <text x="0" y="10" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#ffffff">
          (The Turning Point)
        </text>
      </g>
      
      <!-- SCENE 5 -->
      <g transform="translate(100, 50)">
        <rect x="-80" y="-30" width="160" height="60" fill="#27ae60" rx="8"/>
        <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="16" font-weight="bold" fill="#ffffff">
          SCENE 5：结局
        </text>
        <text x="0" y="10" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#ffffff">
          (The Resolution)
        </text>
      </g>
    </g>
    
    <!-- 连接箭头 -->
    <g opacity="0.6">
      <defs>
        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
          <polygon points="0 0, 10 3.5, 0 7" fill="#7f8c8d"/>
        </marker>
      </defs>
      
      <path d="M -120 -150 L -40 -150" stroke="#7f8c8d" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
      <path d="M 40 -150 L 120 -150" stroke="#7f8c8d" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
      <path d="M 160 -120 L 40 -80" stroke="#7f8c8d" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
      <path d="M -40 -80 L -20 -80" stroke="#7f8c8d" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
    </g>
  </g>
  
  <!-- 底部红字 -->
  <g transform="translate(960, 750)">
    <rect x="-200" y="-25" width="400" height="50" fill="#e74c3c" rx="25"/>
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="32" font-weight="bold" fill="#ffffff">
      冲突，是故事的心跳！
    </text>
  </g>
  
  <!-- 装饰性电影元素 -->
  <g opacity="0.2">
    <!-- 左侧摄像机 -->
    <g transform="translate(200, 400)">
      <rect x="-25" y="-15" width="50" height="30" fill="#2c3e50" rx="5"/>
      <circle cx="-35" cy="0" r="15" fill="#34495e"/>
      <circle cx="-35" cy="0" r="10" fill="#7f8c8d"/>
      <rect x="25" y="-5" width="15" height="10" fill="#2c3e50" rx="2"/>
    </g>
    
    <!-- 右侧胶片 -->
    <g transform="translate(1720, 500)">
      <rect x="-15" y="-40" width="30" height="80" fill="#34495e" rx="3"/>
      <circle cx="-8" cy="-25" r="3" fill="#ecf0f1"/>
      <circle cx="8" cy="-25" r="3" fill="#ecf0f1"/>
      <circle cx="-8" cy="-5" r="3" fill="#ecf0f1"/>
      <circle cx="8" cy="-5" r="3" fill="#ecf0f1"/>
      <circle cx="-8" cy="15" r="3" fill="#ecf0f1"/>
      <circle cx="8" cy="15" r="3" fill="#ecf0f1"/>
      <circle cx="-8" cy="35" r="3" fill="#ecf0f1"/>
      <circle cx="8" cy="35" r="3" fill="#ecf0f1"/>
    </g>
  </g>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.3">
    <path d="M 100 950 Q 960 900 1820 950" stroke="#e74c3c" stroke-width="4" fill="none"/>
  </g>
</svg>
