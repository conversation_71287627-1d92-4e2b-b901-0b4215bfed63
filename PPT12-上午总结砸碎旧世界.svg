<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 砸碎的旧石碑背景 -->
  <g opacity="0.2">
    <!-- 破碎的石碑碎片 -->
    <g transform="translate(400, 400)">
      <path d="M 0 0 L 80 -20 L 120 40 L 60 80 L -20 60 Z" fill="#7f8c8d"/>
      <path d="M 10 10 L 70 -10 L 110 30 L 50 70 L -10 50 Z" fill="#95a5a6"/>
    </g>
    
    <g transform="translate(600, 300) rotate(30)">
      <path d="M 0 0 L 60 -15 L 90 30 L 45 60 L -15 45 Z" fill="#7f8c8d"/>
      <path d="M 8 8 L 52 -7 L 82 22 L 37 52 L -7 37 Z" fill="#95a5a6"/>
    </g>
    
    <g transform="translate(1200, 500) rotate(-20)">
      <path d="M 0 0 L 70 -18 L 105 35 L 52 70 L -18 52 Z" fill="#7f8c8d"/>
      <path d="M 9 9 L 61 -9 L 96 26 L 43 61 L -9 43 Z" fill="#95a5a6"/>
    </g>
    
    <g transform="translate(1400, 350) rotate(45)">
      <path d="M 0 0 L 50 -12 L 75 25 L 37 50 L -12 37 Z" fill="#7f8c8d"/>
      <path d="M 6 6 L 44 -6 L 69 19 L 31 44 L -6 31 Z" fill="#95a5a6"/>
    </g>
  </g>
  
  <!-- 从废墟中升起的新建筑 -->
  <g opacity="0.15">
    <g transform="translate(960, 600)">
      <!-- 新建筑基础 -->
      <rect x="-200" y="0" width="400" height="200" fill="#27ae60"/>
      <rect x="-180" y="20" width="360" height="160" fill="#2ecc71"/>
      
      <!-- 建筑细节 -->
      <rect x="-150" y="50" width="40" height="60" fill="#3498db"/>
      <rect x="-80" y="50" width="40" height="60" fill="#3498db"/>
      <rect x="-10" y="50" width="40" height="60" fill="#3498db"/>
      <rect x="60" y="50" width="40" height="60" fill="#3498db"/>
      <rect x="130" y="50" width="40" height="60" fill="#3498db"/>
      
      <!-- 顶部装饰 -->
      <path d="M -200 0 L 0 -100 L 200 0 Z" fill="#f39c12"/>
    </g>
  </g>
  
  <!-- 主要文字内容 -->
  <g transform="translate(960, 300)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="72" font-weight="bold" fill="#2c3e50">
      看清问题，是解决问题的一半。
    </text>
  </g>
  
  <!-- 总结内容 -->
  <g transform="translate(960, 500)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="36" fill="#7f8c8d">
      上午，我们砸碎了旧世界。
    </text>
    <text x="0" y="60" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="36" fill="#7f8c8d">
      下午，我们创造新武器。
    </text>
  </g>
  
  <!-- 进度指示 -->
  <g transform="translate(960, 700)">
    <!-- 进度条背景 -->
    <rect x="-300" y="-10" width="600" height="20" fill="#ecf0f1" rx="10"/>
    <!-- 进度条填充 -->
    <rect x="-300" y="-10" width="300" height="20" fill="#e74c3c" rx="10"/>
    
    <!-- 阶段标识 -->
    <g transform="translate(-150, 50)">
      <circle cx="0" cy="0" r="30" fill="#e74c3c"/>
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="16" font-weight="bold" fill="#ffffff">
        完成
      </text>
      <text x="0" y="60" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" fill="#2c3e50">
        砸碎旧世界
      </text>
    </g>
    
    <g transform="translate(150, 50)">
      <circle cx="0" cy="0" r="30" fill="#27ae60"/>
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="16" font-weight="bold" fill="#ffffff">
        下一步
      </text>
      <text x="0" y="60" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" fill="#2c3e50">
        打造新武器
      </text>
    </g>
  </g>
  
  <!-- 装饰性锤子图标 -->
  <g transform="translate(200, 200)" opacity="0.3">
    <rect x="-10" y="-60" width="20" height="120" fill="#8e44ad" rx="3"/>
    <rect x="-40" y="-80" width="80" height="40" fill="#9b59b6" rx="5"/>
    <!-- 冲击线 -->
    <path d="M -60 -60 L -80 -80 M -60 -40 L -80 -60 M -60 -20 L -80 -40" stroke="#e74c3c" stroke-width="3"/>
  </g>
  
  <g transform="translate(1720, 800)" opacity="0.3">
    <rect x="-10" y="-60" width="20" height="120" fill="#8e44ad" rx="3"/>
    <rect x="-40" y="-80" width="80" height="40" fill="#9b59b6" rx="5"/>
    <!-- 冲击线 -->
    <path d="M 60 -60 L 80 -80 M 60 -40 L 80 -60 M 60 -20 L 80 -40" stroke="#e74c3c" stroke-width="3"/>
  </g>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.4">
    <path d="M 100 950 Q 960 850 1820 950" stroke="#e74c3c" stroke-width="6" fill="none"/>
    <path d="M 200 980 Q 960 880 1720 980" stroke="#27ae60" stroke-width="4" fill="none"/>
  </g>
</svg>
