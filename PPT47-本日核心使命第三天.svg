<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <g transform="translate(960, 150)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
      本日核心使命
    </text>
  </g>
  
  <!-- 左侧：上午任务 -->
  <g transform="translate(480, 540)">
    <!-- 背景区域 -->
    <rect x="-350" y="-300" width="700" height="600" fill="#ffebee" rx="30"/>
    <rect x="-330" y="-280" width="660" height="560" fill="#ffcdd2" opacity="0.5" rx="25"/>
    
    <!-- 被砸碎的旧石碑图标 -->
    <g transform="translate(0, -150)">
      <!-- 石碑主体 -->
      <rect x="-60" y="-80" width="120" height="160" fill="#95a5a6" rx="10"/>
      <rect x="-50" y="-70" width="100" height="140" fill="#bdc3c7" rx="8"/>
      
      <!-- 裂缝 -->
      <path d="M -40 -60 Q -20 -30 0 0 Q 20 30 40 60" stroke="#e74c3c" stroke-width="4" fill="none"/>
      <path d="M -30 -40 Q -10 -10 10 20 Q 30 50 50 80" stroke="#c0392b" stroke-width="3" fill="none"/>
      
      <!-- 碎片 -->
      <g opacity="0.7">
        <path d="M -80 -40 L -60 -60 L -40 -40 L -60 -20 Z" fill="#7f8c8d"/>
        <path d="M 60 40 L 80 20 L 100 40 L 80 60 Z" fill="#7f8c8d"/>
        <path d="M -70 60 L -50 40 L -30 60 L -50 80 Z" fill="#95a5a6"/>
      </g>
      
      <!-- 冲击波 -->
      <g opacity="0.4">
        <circle cx="0" cy="0" r="100" fill="none" stroke="#e74c3c" stroke-width="3"/>
        <circle cx="0" cy="0" r="120" fill="none" stroke="#e74c3c" stroke-width="2"/>
      </g>
    </g>
    
    <!-- 标题 -->
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="36" font-weight="bold" fill="#e74c3c">
      上午：掌握"认知武器"
    </text>
    
    <!-- 描述 -->
    <text x="0" y="60" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" fill="#2c3e50">
      学习"挑战现状"的方法论，
    </text>
    <text x="0" y="90" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" fill="#2c3e50">
      成为一个能"破旧立新"的
    </text>
    <text x="0" y="120" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" font-weight="bold" fill="#e74c3c">
      思想破局者。
    </text>
    
    <!-- 关键词标签 -->
    <g transform="translate(0, 200)">
      <rect x="-80" y="-20" width="160" height="40" fill="#e74c3c" rx="20"/>
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" font-weight="bold" fill="#ffffff">
        征服"大脑"
      </text>
    </g>
  </g>
  
  <!-- 右侧：下午任务 -->
  <g transform="translate(1440, 540)">
    <!-- 背景区域 -->
    <rect x="-350" y="-300" width="700" height="600" fill="#e8f5e8" rx="30"/>
    <rect x="-330" y="-280" width="660" height="560" fill="#c8e6c9" opacity="0.5" rx="25"/>
    
    <!-- 心脏和声波交织图标 -->
    <g transform="translate(0, -150)">
      <!-- 心脏 -->
      <g>
        <path d="M 0 20 Q -30 -20 -60 0 Q -60 30 0 80 Q 60 30 60 0 Q 30 -20 0 20 Z" fill="#e74c3c"/>
        <path d="M 0 15 Q -25 -15 -50 0 Q -50 25 0 70 Q 50 25 50 0 Q 25 -15 0 15 Z" fill="#c0392b"/>
        
        <!-- 心跳线 -->
        <path d="M -80 0 L -60 0 L -50 -30 L -40 30 L -30 -20 L -20 0 L 20 0 L 30 -20 L 40 30 L 50 -30 L 60 0 L 80 0" 
              stroke="#f39c12" stroke-width="4" fill="none"/>
      </g>
      
      <!-- 声波 -->
      <g opacity="0.6">
        <circle cx="0" cy="0" r="100" fill="none" stroke="#27ae60" stroke-width="3"/>
        <circle cx="0" cy="0" r="120" fill="none" stroke="#2ecc71" stroke-width="2"/>
        <circle cx="0" cy="0" r="140" fill="none" stroke="#27ae60" stroke-width="1"/>
      </g>
    </g>
    
    <!-- 标题 -->
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="36" font-weight="bold" fill="#27ae60">
      下午：注入"情感能量"
    </text>
    
    <!-- 描述 -->
    <text x="0" y="60" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" fill="#2c3e50">
      学习"故事共情"与"全程悬疑"的艺术，
    </text>
    <text x="0" y="90" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" fill="#2c3e50">
      成为一个能"触动人心"的
    </text>
    <text x="0" y="120" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" font-weight="bold" fill="#27ae60">
      情感魔法师。
    </text>
    
    <!-- 关键词标签 -->
    <g transform="translate(0, 200)">
      <rect x="-80" y="-20" width="160" height="40" fill="#27ae60" rx="20"/>
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" font-weight="bold" fill="#ffffff">
        俘获"内心"
      </text>
    </g>
  </g>
  
  <!-- 中间连接元素 -->
  <g transform="translate(960, 540)">
    <circle cx="0" cy="0" r="40" fill="#9b59b6"/>
    <text x="0" y="8" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" font-weight="bold" fill="#ffffff">
      +
    </text>
    
    <!-- 连接线 -->
    <path d="M -400 0 L -50 0" stroke="#e74c3c" stroke-width="4" fill="none" opacity="0.6"/>
    <path d="M 50 0 L 400 0" stroke="#27ae60" stroke-width="4" fill="none" opacity="0.6"/>
  </g>
  
  <!-- 底部总结 -->
  <g transform="translate(960, 850)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="32" fill="#2c3e50">
      今天，我们将完成从"结构设计师"到"思想家"与"艺术家"的终极蜕变。
    </text>
  </g>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.3">
    <path d="M 100 950 Q 960 900 1820 950" stroke="#9b59b6" stroke-width="4" fill="none"/>
  </g>
</svg>
