<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <g transform="translate(960, 120)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
      技巧：用"悬疑钩子"串联你的珍珠
    </text>
  </g>
  
  <!-- 主视觉：鱼线串珍珠 -->
  <g transform="translate(960, 350)">
    <!-- 鱼线 -->
    <path d="M -400 0 Q -200 -50 0 0 Q 200 50 400 0" stroke="#7f8c8d" stroke-width="4" fill="none"/>
    
    <!-- 珍珠 -->
    <g>
      <!-- 珍珠1 -->
      <g transform="translate(-300, -25)">
        <circle cx="0" cy="0" r="25" fill="#f1c40f"/>
        <circle cx="0" cy="0" r="20" fill="#f39c12"/>
        <circle cx="0" cy="0" r="15" fill="#e67e22"/>
        <circle cx="-8" cy="-8" r="5" fill="#ffffff" opacity="0.8"/>
      </g>
      
      <!-- 珍珠2 -->
      <g transform="translate(-100, -40)">
        <circle cx="0" cy="0" r="25" fill="#e74c3c"/>
        <circle cx="0" cy="0" r="20" fill="#c0392b"/>
        <circle cx="0" cy="0" r="15" fill="#a93226"/>
        <circle cx="-8" cy="-8" r="5" fill="#ffffff" opacity="0.8"/>
      </g>
      
      <!-- 珍珠3 -->
      <g transform="translate(100, 40)">
        <circle cx="0" cy="0" r="25" fill="#3498db"/>
        <circle cx="0" cy="0" r="20" fill="#2980b9"/>
        <circle cx="0" cy="0" r="15" fill="#1f4e79"/>
        <circle cx="-8" cy="-8" r="5" fill="#ffffff" opacity="0.8"/>
      </g>
      
      <!-- 珍珠4 -->
      <g transform="translate(300, 25)">
        <circle cx="0" cy="0" r="25" fill="#27ae60"/>
        <circle cx="0" cy="0" r="20" fill="#2ecc71"/>
        <circle cx="0" cy="0" r="15" fill="#1e8449"/>
        <circle cx="-8" cy="-8" r="5" fill="#ffffff" opacity="0.8"/>
      </g>
    </g>
    
    <!-- 鱼钩 -->
    <g transform="translate(400, 0)">
      <path d="M 0 0 Q 20 10 30 0 Q 35 -5 30 -10 Q 25 -15 20 -10" 
            stroke="#95a5a6" stroke-width="6" fill="none"/>
      <circle cx="25" cy="-5" r="3" fill="#f39c12"/>
      
      <!-- 闪光效果 -->
      <g opacity="0.8">
        <line x1="20" y1="-15" x2="25" y2="-20" stroke="#f1c40f" stroke-width="2"/>
        <line x1="30" y1="-12" x2="35" y2="-17" stroke="#f1c40f" stroke-width="2"/>
        <line x1="35" y1="-5" x2="40" y2="-10" stroke="#f1c40f" stroke-width="2"/>
      </g>
    </g>
  </g>
  
  <!-- 核心理念 -->
  <g transform="translate(960, 500)">
    <rect x="-400" y="-30" width="800" height="60" fill="#f8f9fa" rx="30"/>
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" font-weight="bold" fill="#2c3e50">
      不断地制造"认知缺口"，让学员始终带着好奇心。
    </text>
  </g>
  
  <!-- 三大技巧 -->
  <g transform="translate(960, 650)">
    <!-- 技巧1：开篇设悬 -->
    <g transform="translate(-250, 0)">
      <rect x="-120" y="-60" width="240" height="120" fill="#e8f6f3" rx="15"/>
      <rect x="-100" y="-40" width="200" height="80" fill="#c8e6c9" opacity="0.5" rx="10"/>
      
      <!-- 问号图标 -->
      <g transform="translate(0, -20)">
        <circle cx="0" cy="0" r="20" fill="#27ae60"/>
        <text x="0" y="8" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" font-weight="bold" fill="#ffffff">
          ?
        </text>
      </g>
      
      <text x="0" y="30" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="20" font-weight="bold" fill="#27ae60">
        开篇设悬
      </text>
    </g>
    
    <!-- 技巧2：转场提问 -->
    <g transform="translate(0, 0)">
      <rect x="-120" y="-60" width="240" height="120" fill="#fef5e7" rx="15"/>
      <rect x="-100" y="-40" width="200" height="80" fill="#fdeaa7" opacity="0.5" rx="10"/>
      
      <!-- 箭头图标 -->
      <g transform="translate(0, -20)">
        <circle cx="0" cy="0" r="20" fill="#f39c12"/>
        <path d="M -8 0 L 8 0 M 3 -5 L 8 0 L 3 5" stroke="#ffffff" stroke-width="3" fill="none"/>
      </g>
      
      <text x="0" y="30" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="20" font-weight="bold" fill="#f39c12">
        转场提问
      </text>
    </g>
    
    <!-- 技巧3：结尾留扣 -->
    <g transform="translate(250, 0)">
      <rect x="-120" y="-60" width="240" height="120" fill="#ebf3fd" rx="15"/>
      <rect x="-100" y="-40" width="200" height="80" fill="#c8e6c9" opacity="0.3" rx="10"/>
      
      <!-- 钩子图标 -->
      <g transform="translate(0, -20)">
        <circle cx="0" cy="0" r="20" fill="#3498db"/>
        <path d="M 0 -10 Q 10 -5 10 5 Q 10 15 0 15 Q -5 15 -5 10" 
              stroke="#ffffff" stroke-width="3" fill="none"/>
      </g>
      
      <text x="0" y="30" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="20" font-weight="bold" fill="#3498db">
        结尾留扣
      </text>
    </g>
  </g>
  
  <!-- 装饰性悬疑元素 -->
  <g opacity="0.2">
    <!-- 左侧放大镜 -->
    <g transform="translate(200, 400)">
      <circle cx="0" cy="0" r="20" fill="none" stroke="#95a5a6" stroke-width="4"/>
      <circle cx="0" cy="0" r="15" fill="#ecf0f1"/>
      <rect x="15" y="15" width="4" height="20" fill="#95a5a6" rx="2" transform="rotate(45 17 25)"/>
    </g>
    
    <!-- 右侧钥匙孔 -->
    <g transform="translate(1720, 500)">
      <circle cx="0" cy="0" r="15" fill="#7f8c8d"/>
      <circle cx="0" cy="0" r="10" fill="#95a5a6"/>
      <rect x="-2" y="0" width="4" height="12" fill="#95a5a6"/>
    </g>
  </g>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.3">
    <path d="M 100 950 Q 960 900 1820 950" stroke="#f39c12" stroke-width="4" fill="none"/>
  </g>
</svg>
