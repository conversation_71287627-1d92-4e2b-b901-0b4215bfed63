<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 建筑骨架背景 -->
  <g opacity="0.15">
    <!-- 地基 -->
    <rect x="0" y="700" width="1920" height="380" fill="#34495e"/>
    <rect x="0" y="720" width="1920" height="340" fill="#2c3e50"/>
    
    <!-- 建筑骨架 -->
    <g transform="translate(960, 600)">
      <!-- 主体框架 -->
      <rect x="-300" y="-200" width="600" height="400" fill="none" stroke="#7f8c8d" stroke-width="8"/>
      <rect x="-250" y="-150" width="500" height="300" fill="none" stroke="#95a5a6" stroke-width="6"/>
      
      <!-- 横梁 -->
      <line x1="-300" y1="-100" x2="300" y2="-100" stroke="#7f8c8d" stroke-width="6"/>
      <line x1="-300" y1="0" x2="300" y2="0" stroke="#7f8c8d" stroke-width="6"/>
      <line x1="-300" y1="100" x2="300" y2="100" stroke="#7f8c8d" stroke-width="6"/>
      
      <!-- 竖梁 -->
      <line x1="-150" y1="-200" x2="-150" y2="200" stroke="#7f8c8d" stroke-width="6"/>
      <line x1="0" y1="-200" x2="0" y2="200" stroke="#7f8c8d" stroke-width="6"/>
      <line x1="150" y1="-200" x2="150" y2="200" stroke="#7f8c8d" stroke-width="6"/>
      
      <!-- 对角支撑 -->
      <line x1="-250" y1="-150" x2="-100" y2="0" stroke="#95a5a6" stroke-width="4"/>
      <line x1="250" y1="-150" x2="100" y2="0" stroke="#95a5a6" stroke-width="4"/>
      <line x1="-100" y1="0" x2="-250" y2="150" stroke="#95a5a6" stroke-width="4"/>
      <line x1="100" y1="0" x2="250" y2="150" stroke="#95a5a6" stroke-width="4"/>
    </g>
    
    <!-- 阳光穿透效果 -->
    <g opacity="0.3">
      <path d="M 200 100 L 300 300 L 250 300 L 150 100 Z" fill="#f1c40f"/>
      <path d="M 400 150 L 500 350 L 450 350 L 350 150 Z" fill="#f39c12"/>
      <path d="M 1420 120 L 1520 320 L 1470 320 L 1370 120 Z" fill="#f1c40f"/>
      <path d="M 1620 180 L 1720 380 L 1670 380 L 1570 180 Z" fill="#f39c12"/>
    </g>
  </g>
  
  <!-- DAY 2 标识 -->
  <g transform="translate(960, 300)">
    <!-- 背景圆形装饰 -->
    <circle cx="0" cy="0" r="150" fill="#3498db" opacity="0.1"/>
    <circle cx="0" cy="0" r="120" fill="#2980b9" opacity="0.1"/>
    
    <!-- DAY 2 文字 -->
    <text x="0" y="20" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="120" font-weight="bold" fill="#3498db">
      DAY 2
    </text>
  </g>
  
  <!-- 主标题 -->
  <g transform="translate(960, 500)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
      信任与结构：用"框架"构建信赖，用"开场"点燃渴望
    </text>
  </g>
  
  <!-- 装饰性建筑元素 -->
  <g opacity="0.2">
    <!-- 左侧建筑轮廓 -->
    <g transform="translate(200, 600)">
      <path d="M 0 200 L 0 0 L 50 -50 L 100 0 L 100 200 Z" fill="#3498db"/>
      <rect x="20" y="50" width="15" height="20" fill="#ffffff"/>
      <rect x="45" y="50" width="15" height="20" fill="#ffffff"/>
      <rect x="70" y="50" width="15" height="20" fill="#ffffff"/>
      <rect x="20" y="90" width="15" height="20" fill="#ffffff"/>
      <rect x="45" y="90" width="15" height="20" fill="#ffffff"/>
      <rect x="70" y="90" width="15" height="20" fill="#ffffff"/>
    </g>
    
    <!-- 右侧建筑轮廓 -->
    <g transform="translate(1620, 600)">
      <path d="M 0 200 L 0 -20 L 50 -70 L 100 -20 L 100 200 Z" fill="#27ae60"/>
      <rect x="20" y="30" width="15" height="20" fill="#ffffff"/>
      <rect x="45" y="30" width="15" height="20" fill="#ffffff"/>
      <rect x="70" y="30" width="15" height="20" fill="#ffffff"/>
      <rect x="20" y="70" width="15" height="20" fill="#ffffff"/>
      <rect x="45" y="70" width="15" height="20" fill="#ffffff"/>
      <rect x="70" y="70" width="15" height="20" fill="#ffffff"/>
    </g>
  </g>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.4">
    <path d="M 200 700 Q 960 600 1720 700" stroke="#3498db" stroke-width="8" fill="none"/>
    <path d="M 300 750 Q 960 650 1620 750" stroke="#27ae60" stroke-width="6" fill="none"/>
  </g>
  
  <!-- 底部装饰线条 -->
  <g opacity="0.3">
    <rect x="0" y="950" width="1920" height="4" fill="#3498db"/>
    <rect x="0" y="970" width="1920" height="3" fill="#27ae60"/>
    <rect x="0" y="985" width="1920" height="2" fill="#f39c12"/>
  </g>
  
  <!-- 建筑工具装饰 -->
  <g transform="translate(300, 150)" opacity="0.2">
    <!-- 水平仪 -->
    <rect x="-40" y="-5" width="80" height="10" fill="#f39c12" rx="5"/>
    <circle cx="0" cy="0" r="8" fill="#e67e22"/>
    <circle cx="0" cy="0" r="3" fill="#ffffff"/>
  </g>
  
  <g transform="translate(1620, 200)" opacity="0.2">
    <!-- 三角尺 -->
    <path d="M 0 0 L 40 0 L 0 40 Z" fill="#9b59b6"/>
    <path d="M 5 5 L 35 5 L 5 35 Z" fill="#8e44ad"/>
  </g>
</svg>
