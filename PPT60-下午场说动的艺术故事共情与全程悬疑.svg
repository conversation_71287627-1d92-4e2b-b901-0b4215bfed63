<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 篝火晚会背景 -->
  <g opacity="0.15">
    <!-- 夜空 -->
    <rect x="0" y="0" width="1920" height="600" fill="#2c3e50"/>
    <rect x="0" y="600" width="1920" height="480" fill="#34495e"/>
    
    <!-- 星星 -->
    <g opacity="0.6">
      <circle cx="200" cy="100" r="2" fill="#f1c40f"/>
      <circle cx="400" cy="80" r="3" fill="#f39c12"/>
      <circle cx="600" cy="120" r="2" fill="#f1c40f"/>
      <circle cx="800" cy="60" r="2" fill="#f39c12"/>
      <circle cx="1000" cy="140" r="3" fill="#f1c40f"/>
      <circle cx="1200" cy="90" r="2" fill="#f39c12"/>
      <circle cx="1400" cy="110" r="2" fill="#f1c40f"/>
      <circle cx="1600" cy="70" r="3" fill="#f39c12"/>
      <circle cx="1800" cy="130" r="2" fill="#f1c40f"/>
    </g>
    
    <!-- 篝火 -->
    <g transform="translate(960, 700)">
      <!-- 木柴 -->
      <rect x="-40" y="20" width="80" height="8" fill="#8e44ad" rx="4"/>
      <rect x="-30" y="10" width="60" height="8" fill="#9b59b6" rx="4" transform="rotate(30)"/>
      <rect x="-35" y="15" width="70" height="8" fill="#8e44ad" rx="4" transform="rotate(-20)"/>
      
      <!-- 火焰 -->
      <g>
        <ellipse cx="0" cy="10" rx="50" ry="20" fill="#e67e22"/>
        <path d="M -40 10 Q -20 -30 0 10 Q 20 -30 40 10" fill="#e74c3c"/>
        <path d="M -30 5 Q -10 -40 10 5 Q 30 -40 30 5" fill="#f39c12"/>
        <path d="M -20 0 Q 0 -50 20 0" fill="#f1c40f"/>
        <path d="M -10 -5 Q 0 -60 10 -5" fill="#ffffff" opacity="0.8"/>
      </g>
      
      <!-- 火花 -->
      <g opacity="0.8">
        <circle cx="-60" cy="-10" r="3" fill="#f39c12"/>
        <circle cx="60" cy="-5" r="2" fill="#e74c3c"/>
        <circle cx="-50" cy="-40" r="2" fill="#f1c40f"/>
        <circle cx="50" cy="-35" r="3" fill="#e67e22"/>
      </g>
    </g>
    
    <!-- 围坐的人群剪影 -->
    <g opacity="0.4">
      <!-- 左侧人群 -->
      <g transform="translate(400, 800)">
        <circle cx="0" cy="-20" r="15" fill="#2c3e50"/>
        <rect x="-12" y="-5" width="24" height="30" fill="#2c3e50" rx="4"/>
      </g>
      
      <g transform="translate(500, 820)">
        <circle cx="0" cy="-20" r="15" fill="#2c3e50"/>
        <rect x="-12" y="-5" width="24" height="30" fill="#2c3e50" rx="4"/>
      </g>
      
      <!-- 右侧人群 -->
      <g transform="translate(1420, 800)">
        <circle cx="0" cy="-20" r="15" fill="#2c3e50"/>
        <rect x="-12" y="-5" width="24" height="30" fill="#2c3e50" rx="4"/>
      </g>
      
      <g transform="translate(1520, 820)">
        <circle cx="0" cy="-20" r="15" fill="#2c3e50"/>
        <rect x="-12" y="-5" width="24" height="30" fill="#2c3e50" rx="4"/>
      </g>
      
      <!-- 讲故事的人（中央） -->
      <g transform="translate(960, 600)">
        <circle cx="0" cy="-30" r="20" fill="#e74c3c"/>
        <rect x="-15" y="-10" width="30" height="40" fill="#e74c3c" rx="5"/>
        
        <!-- 手势 -->
        <rect x="-35" y="-5" width="20" height="8" fill="#e74c3c" rx="4" transform="rotate(-20 -25 -1)"/>
        <rect x="15" y="-5" width="20" height="8" fill="#e74c3c" rx="4" transform="rotate(20 25 -1)"/>
      </g>
    </g>
  </g>
  
  <!-- 时间标识 -->
  <g transform="translate(960, 250)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="48" font-weight="bold" fill="#e74c3c">
      下午场 (14:00 - 17:00)
    </text>
  </g>
  
  <!-- 主标题 -->
  <g transform="translate(960, 400)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="64" font-weight="bold" fill="#2c3e50">
      「说动」的艺术：故事共情与全程悬疑
    </text>
  </g>
  
  <!-- 装饰性故事元素 -->
  <g opacity="0.3">
    <!-- 左侧书本 -->
    <g transform="translate(200, 600)">
      <rect x="-25" y="-35" width="50" height="70" fill="#8e44ad" rx="5"/>
      <rect x="-20" y="-30" width="40" height="60" fill="#9b59b6" rx="3"/>
      
      <!-- 书页线条 -->
      <line x1="-15" y1="-15" x2="15" y2="-15" stroke="#ffffff" stroke-width="2"/>
      <line x1="-15" y1="-5" x2="15" y2="-5" stroke="#ffffff" stroke-width="2"/>
      <line x1="-15" y1="5" x2="15" y2="5" stroke="#ffffff" stroke-width="2"/>
      <line x1="-15" y1="15" x2="10" y2="15" stroke="#ffffff" stroke-width="2"/>
    </g>
    
    <!-- 右侧心形 -->
    <g transform="translate(1720, 500)">
      <path d="M 0 15 Q -20 -10 -40 0 Q -40 20 0 50 Q 40 20 40 0 Q 20 -10 0 15 Z" fill="#e74c3c"/>
      <path d="M 0 10 Q -15 -5 -30 0 Q -30 15 0 40 Q 30 15 30 0 Q 15 -5 0 10 Z" fill="#c0392b"/>
    </g>
    
    <!-- 底部魔法棒 -->
    <g transform="translate(960, 800)">
      <rect x="-2" y="-40" width="4" height="80" fill="#8e44ad" rx="2"/>
      <circle cx="0" cy="-40" r="8" fill="#f1c40f"/>
      <circle cx="0" cy="-40" r="5" fill="#f39c12"/>
      
      <!-- 魔法星星 -->
      <g opacity="0.6">
        <path d="M -15 -55 L -12 -48 L -5 -50 L -12 -42 L -15 -35 L -18 -42 L -25 -40 L -18 -48 Z" fill="#f1c40f"/>
        <path d="M 15 -60 L 17 -55 L 22 -56 L 17 -51 L 15 -46 L 13 -51 L 8 -50 L 13 -55 Z" fill="#f39c12"/>
      </g>
    </g>
  </g>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.4">
    <path d="M 200 900 Q 960 800 1720 900" stroke="#e74c3c" stroke-width="8" fill="none"/>
    <path d="M 300 950 Q 960 850 1620 950" stroke="#f39c12" stroke-width="6" fill="none"/>
  </g>
  
  <!-- 底部装饰 -->
  <g transform="translate(960, 950)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" fill="#7f8c8d">
      学习一门"魔法"，让观点直达人心
    </text>
  </g>
  
  <!-- 边框装饰 -->
  <rect x="30" y="30" width="1860" height="1020" fill="none" stroke="#e74c3c" stroke-width="3" opacity="0.2" rx="15"/>
</svg>
