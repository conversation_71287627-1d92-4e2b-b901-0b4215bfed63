<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <g transform="translate(960, 120)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
      最终整合：你的"大片"课程蓝图
    </text>
  </g>
  
  <!-- 主视觉：桌子上的创作元素汇聚成胶片 -->
  <g transform="translate(960, 450)">
    <!-- 桌面 -->
    <ellipse cx="0" cy="150" rx="500" ry="100" fill="#8e44ad"/>
    <ellipse cx="0" cy="145" rx="480" ry="90" fill="#9b59b6"/>
    
    <!-- 散落的元素 -->
    <!-- 钉子 -->
    <g transform="translate(-300, 50)">
      <rect x="-5" y="-30" width="10" height="60" fill="#e74c3c" rx="2"/>
      <circle cx="0" cy="-35" r="8" fill="#c0392b"/>
      <text x="0" y="80" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#e74c3c">
        钉子定位
      </text>
    </g>
    
    <!-- 地图 -->
    <g transform="translate(-150, 30)">
      <rect x="-25" y="-20" width="50" height="40" fill="#f39c12" rx="3"/>
      <rect x="-20" y="-15" width="40" height="30" fill="#e67e22" rx="2"/>
      
      <!-- 地图线条 -->
      <g opacity="0.6">
        <line x1="-15" y1="-8" x2="15" y2="-8" stroke="#ffffff" stroke-width="1"/>
        <line x1="-15" y1="0" x2="15" y2="0" stroke="#ffffff" stroke-width="1"/>
        <line x1="-15" y1="8" x2="10" y2="8" stroke="#ffffff" stroke-width="1"/>
      </g>
      
      <text x="0" y="60" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#f39c12">
        高信任框架
      </text>
    </g>
    
    <!-- 剧本 -->
    <g transform="translate(0, 20)">
      <rect x="-30" y="-25" width="60" height="50" fill="#3498db" rx="5"/>
      <rect x="-25" y="-20" width="50" height="40" fill="#2980b9" rx="3"/>
      
      <!-- 剧本内容 -->
      <g opacity="0.8">
        <line x1="-20" y1="-10" x2="20" y2="-10" stroke="#ffffff" stroke-width="1"/>
        <line x1="-20" y1="-2" x2="20" y2="-2" stroke="#ffffff" stroke-width="1"/>
        <line x1="-20" y1="6" x2="15" y2="6" stroke="#ffffff" stroke-width="1"/>
        <line x1="-20" y1="14" x2="18" y2="14" stroke="#ffffff" stroke-width="1"/>
      </g>
      
      <text x="0" y="70" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#3498db">
        黄金开场
      </text>
    </g>
    
    <!-- 故事板 -->
    <g transform="translate(150, 40)">
      <rect x="-25" y="-20" width="50" height="40" fill="#27ae60" rx="3"/>
      <rect x="-20" y="-15" width="40" height="30" fill="#2ecc71" rx="2"/>
      
      <!-- 故事板格子 -->
      <g opacity="0.6">
        <rect x="-15" y="-10" width="12" height="8" fill="#ffffff"/>
        <rect x="-1" y="-10" width="12" height="8" fill="#ffffff"/>
        <rect x="-15" y="2" width="12" height="8" fill="#ffffff"/>
        <rect x="-1" y="2" width="12" height="8" fill="#ffffff"/>
      </g>
      
      <text x="0" y="70" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#27ae60">
        核心故事
      </text>
    </g>
    
    <!-- 其他元素 -->
    <g transform="translate(300, 60)">
      <rect x="-20" y="-15" width="40" height="30" fill="#9b59b6" rx="3"/>
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="12" fill="#ffffff">
        收场
      </text>
      <text x="0" y="50" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#9b59b6">
        强力收场
      </text>
    </g>
    
    <g transform="translate(-200, 100)">
      <rect x="-20" y="-15" width="40" height="30" fill="#e67e22" rx="3"/>
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="12" fill="#ffffff">
        转化
      </text>
      <text x="0" y="50" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="14" fill="#e67e22">
        转化清单
      </text>
    </g>
    
    <!-- 汇聚成的完整胶片 -->
    <g transform="translate(0, -150)">
      <!-- 胶片主体 -->
      <rect x="-150" y="-50" width="300" height="100" fill="#2c3e50" rx="10"/>
      <rect x="-140" y="-40" width="280" height="80" fill="#34495e" rx="8"/>
      
      <!-- 胶片孔 -->
      <g opacity="0.6">
        <circle cx="-120" cy="-25" r="5" fill="#ecf0f1"/>
        <circle cx="-120" cy="-5" r="5" fill="#ecf0f1"/>
        <circle cx="-120" cy="15" r="5" fill="#ecf0f1"/>
        <circle cx="-120" cy="35" r="5" fill="#ecf0f1"/>
        
        <circle cx="120" cy="-25" r="5" fill="#ecf0f1"/>
        <circle cx="120" cy="-5" r="5" fill="#ecf0f1"/>
        <circle cx="120" cy="15" r="5" fill="#ecf0f1"/>
        <circle cx="120" cy="35" r="5" fill="#ecf0f1"/>
      </g>
      
      <!-- 胶片内容 -->
      <rect x="-100" y="-30" width="200" height="60" fill="#f1c40f" rx="5"/>
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="20" font-weight="bold" fill="#2c3e50">
        完整大片蓝图
      </text>
    </g>
    
    <!-- 汇聚箭头 -->
    <g opacity="0.6">
      <defs>
        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
          <polygon points="0 0, 10 3.5, 0 7" fill="#f39c12"/>
        </marker>
      </defs>
      
      <path d="M -250 0 L -120 -100" stroke="#f39c12" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
      <path d="M -100 -20 L -50 -120" stroke="#f39c12" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
      <path d="M 50 -30 L 50 -120" stroke="#f39c12" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
      <path d="M 200 -10 L 120 -100" stroke="#f39c12" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
    </g>
  </g>
  
  <!-- 核心动作说明 -->
  <g transform="translate(960, 700)">
    <rect x="-450" y="-30" width="900" height="60" fill="#f8f9fa" rx="30"/>
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" font-weight="bold" fill="#2c3e50">
      将四天所有工作坊成果，整合成一份完整的"导演手册"。
    </text>
  </g>
  
  <!-- 包含内容列表 -->
  <g transform="translate(960, 800)">
    <rect x="-500" y="-60" width="1000" height="120" fill="#2c3e50" rx="20"/>
    
    <text x="-300" y="-25" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" font-weight="bold" fill="#ffffff">
      《钉子定位画布》
    </text>
    <text x="-100" y="-25" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" font-weight="bold" fill="#ffffff">
      《高信任度框架》
    </text>
    <text x="100" y="-25" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" font-weight="bold" fill="#ffffff">
      《黄金开场》
    </text>
    <text x="300" y="-25" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" font-weight="bold" fill="#ffffff">
      《核心故事》
    </text>
    
    <text x="-200" y="25" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" font-weight="bold" fill="#ffffff">
      《强力收场》
    </text>
    <text x="200" y="25" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="18" font-weight="bold" fill="#ffffff">
      《转化清单》
    </text>
  </g>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.3">
    <path d="M 100 1000 Q 960 950 1820 1000" stroke="#f39c12" stroke-width="4" fill="none"/>
  </g>
</svg>
