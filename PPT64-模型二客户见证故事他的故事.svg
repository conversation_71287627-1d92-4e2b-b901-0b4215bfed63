<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <g transform="translate(960, 120)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
      模型二：客户见证故事 (他的故事)
    </text>
  </g>
  
  <!-- 主视觉：一个人正在帮助另一个人爬上山顶 -->
  <g transform="translate(960, 350)">
    <!-- 山峰背景 -->
    <g opacity="0.3">
      <path d="M -400 200 L -200 50 L 0 100 L 200 0 L 400 150 L 500 200 L -500 200 Z" fill="#95a5a6"/>
      <path d="M -350 180 L -150 70 L 50 120 L 250 20 L 450 170 L 500 200 L -500 200 Z" fill="#bdc3c7"/>
    </g>
    
    <!-- 山顶场景 -->
    <g transform="translate(200, -50)">
      <!-- 导师（帮助者） -->
      <g transform="translate(-50, 0)">
        <circle cx="0" cy="-30" r="20" fill="#27ae60"/>
        <rect x="-15" y="-10" width="30" height="40" fill="#27ae60" rx="5"/>
        
        <!-- 伸出援手 -->
        <rect x="15" y="-5" width="25" height="8" fill="#27ae60" rx="4" transform="rotate(-20 27 -1)"/>
        
        <!-- 导师光环 -->
        <circle cx="0" cy="-30" r="35" fill="none" stroke="#2ecc71" stroke-width="3" opacity="0.4"/>
      </g>
      
      <!-- 学员（被帮助者） -->
      <g transform="translate(50, 20)">
        <circle cx="0" cy="-30" r="18" fill="#3498db"/>
        <rect x="-13" y="-10" width="26" height="35" fill="#3498db" rx="4"/>
        
        <!-- 伸手求助 -->
        <rect x="-35" y="-5" width="20" height="6" fill="#3498db" rx="3" transform="rotate(30 -25 -2)"/>
        
        <!-- 努力攀爬的姿态 -->
        <rect x="-20" y="25" width="12" height="25" fill="#3498db"/>
        <rect x="8" y="25" width="12" height="25" fill="#3498db"/>
      </g>
      
      <!-- 连接的手 -->
      <g transform="translate(0, -5)">
        <ellipse cx="0" cy="0" rx="15" ry="8" fill="#f39c12" opacity="0.8"/>
        <ellipse cx="0" cy="0" rx="10" ry="5" fill="#e67e22"/>
      </g>
      
      <!-- 成功标记 -->
      <g transform="translate(0, -80)">
        <circle cx="0" cy="0" r="20" fill="#f1c40f"/>
        <path d="M -10 0 L -3 7 L 10 -7" stroke="#ffffff" stroke-width="4" fill="none"/>
      </g>
    </g>
    
    <!-- 攀登路径 -->
    <g opacity="0.6">
      <path d="M -300 150 Q -100 100 0 80 Q 100 60 200 -30" 
            stroke="#3498db" stroke-width="4" fill="none" stroke-dasharray="8,4"/>
      
      <!-- 足迹 -->
      <g>
        <ellipse cx="-250" cy="130" rx="8" ry="4" fill="#3498db" opacity="0.4"/>
        <ellipse cx="-150" cy="90" rx="8" ry="4" fill="#3498db" opacity="0.4"/>
        <ellipse cx="-50" cy="70" rx="8" ry="4" fill="#3498db" opacity="0.4"/>
        <ellipse cx="50" cy="50" rx="8" ry="4" fill="#3498db" opacity="0.4"/>
        <ellipse cx="150" cy="10" rx="8" ry="4" fill="#3498db" opacity="0.4"/>
      </g>
    </g>
  </g>
  
  <!-- 故事框架 -->
  <g transform="translate(960, 600)">
    <!-- 框架背景 -->
    <rect x="-450" y="-100" width="900" height="200" fill="#e8f5e8" rx="20"/>
    <rect x="-430" y="-80" width="860" height="160" fill="#c8e6c9" opacity="0.5" rx="15"/>
    
    <!-- 三个阶段 -->
    <g transform="translate(-250, -30)">
      <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="20" font-weight="bold" fill="#3498db">
        "让我想起我的学员小王，他刚来时..." (起点)
      </text>
    </g>
    
    <g transform="translate(0, 0)">
      <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="20" font-weight="bold" fill="#f39c12">
        "我只教了他一招，他回去后..." (转折)
      </text>
    </g>
    
    <g transform="translate(250, 30)">
      <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="20" font-weight="bold" fill="#27ae60">
        "三个月后，他不仅...还..." (终点)
      </text>
    </g>
  </g>
  
  <!-- 核心价值 -->
  <g transform="translate(960, 800)">
    <rect x="-400" y="-30" width="800" height="60" fill="#27ae60" rx="30"/>
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" font-weight="bold" fill="#ffffff">
      你的方法论，是经过真人验证的，是有效的。
    </text>
  </g>
  
  <!-- 装饰性见证元素 -->
  <g opacity="0.2">
    <!-- 左侧奖杯 -->
    <g transform="translate(200, 400)">
      <rect x="-15" y="-20" width="30" height="35" fill="#f1c40f" rx="5"/>
      <rect x="-20" y="15" width="40" height="12" fill="#e67e22" rx="3"/>
      <rect x="-10" y="27" width="20" height="15" fill="#d35400" rx="3"/>
      
      <!-- 奖杯装饰 -->
      <circle cx="-25" cy="-10" r="8" fill="#f39c12"/>
      <circle cx="25" cy="-10" r="8" fill="#f39c12"/>
    </g>
    
    <!-- 右侧证书 -->
    <g transform="translate(1720, 500)">
      <rect x="-25" y="-20" width="50" height="40" fill="#ecf0f1" rx="3"/>
      <rect x="-20" y="-15" width="40" height="30" fill="#bdc3c7" rx="2"/>
      
      <!-- 证书内容线条 -->
      <line x1="-15" y1="-8" x2="15" y2="-8" stroke="#7f8c8d" stroke-width="2"/>
      <line x1="-15" y1="-2" x2="15" y2="-2" stroke="#7f8c8d" stroke-width="2"/>
      <line x1="-15" y1="4" x2="10" y2="4" stroke="#7f8c8d" stroke-width="2"/>
      
      <!-- 印章 -->
      <circle cx="10" cy="10" r="6" fill="#e74c3c"/>
    </g>
  </g>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.3">
    <path d="M 100 950 Q 960 900 1820 950" stroke="#27ae60" stroke-width="4" fill="none"/>
  </g>
</svg>
