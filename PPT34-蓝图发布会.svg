<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 舞台背景 -->
  <g opacity="0.1">
    <rect x="0" y="600" width="1920" height="480" fill="#2c3e50"/>
    <!-- 舞台纹理 -->
    <rect x="0" y="620" width="1920" height="20" fill="#34495e"/>
    <rect x="0" y="660" width="1920" height="15" fill="#34495e"/>
    <rect x="0" y="700" width="1920" height="10" fill="#34495e"/>
  </g>
  
  <!-- 标题 -->
  <g transform="translate(960, 150)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
      蓝图发布会
    </text>
  </g>
  
  <!-- 主视觉：聚光灯打在白板上 -->
  <g transform="translate(960, 400)">
    <!-- 聚光灯光束 -->
    <defs>
      <radialGradient id="spotlight" cx="50%" cy="0%" r="50%">
        <stop offset="0%" style="stop-color:#f1c40f;stop-opacity:0.8"/>
        <stop offset="50%" style="stop-color:#f39c12;stop-opacity:0.4"/>
        <stop offset="100%" style="stop-color:#e67e22;stop-opacity:0.1"/>
      </radialGradient>
    </defs>
    
    <ellipse cx="0" cy="50" rx="200" ry="250" fill="url(#spotlight)"/>
    
    <!-- 白板 -->
    <g transform="translate(0, 0)">
      <rect x="-150" y="-80" width="300" height="160" fill="#ffffff" rx="10"/>
      <rect x="-140" y="-70" width="280" height="140" fill="#f8f9fa" rx="8"/>
      
      <!-- 白板边框 -->
      <rect x="-150" y="-80" width="300" height="160" fill="none" stroke="#bdc3c7" stroke-width="3" rx="10"/>
      
      <!-- 示意性框架图 -->
      <g opacity="0.6">
        <circle cx="-80" cy="-30" r="15" fill="#e74c3c"/>
        <text x="-80" y="-25" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="12" fill="#ffffff">
          1
        </text>
        
        <path d="M -65 -30 L -35 -30" stroke="#7f8c8d" stroke-width="3" marker-end="url(#arrowhead)"/>
        
        <circle cx="-20" cy="-30" r="15" fill="#f39c12"/>
        <text x="-20" y="-25" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="12" fill="#ffffff">
          2
        </text>
        
        <path d="M -5 -30 L 25 -30" stroke="#7f8c8d" stroke-width="3" marker-end="url(#arrowhead)"/>
        
        <circle cx="40" cy="-30" r="15" fill="#27ae60"/>
        <text x="40" y="-25" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="12" fill="#ffffff">
          3
        </text>
        
        <!-- 框架标题 -->
        <text x="0" y="20" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="16" fill="#2c3e50">
          解决方案地图
        </text>
      </g>
    </g>
    
    <!-- 聚光灯设备 -->
    <g transform="translate(0, -200)" opacity="0.4">
      <circle cx="0" cy="0" r="20" fill="#34495e"/>
      <rect x="-15" y="0" width="30" height="40" fill="#2c3e50" rx="5"/>
      <path d="M 0 40 L -30 150 L 30 150 Z" fill="#f1c40f" opacity="0.3"/>
    </g>
  </g>
  
  <!-- 规则说明 -->
  <g transform="translate(960, 650)">
    <!-- 目标 -->
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="32" font-weight="bold" fill="#e74c3c">
      目标：用1分钟，向"投资人"清晰地讲解你的解决方案地图，并赢得他们的信任。
    </text>
    
    <!-- 规则框 -->
    <g transform="translate(0, 80)">
      <rect x="-400" y="-40" width="800" height="120" fill="#f8f9fa" rx="15"/>
      <rect x="-380" y="-20" width="760" height="80" fill="#e9ecef" opacity="0.5" rx="10"/>
      
      <text x="0" y="-30" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" font-weight="bold" fill="#2c3e50">
        规则：
      </text>
      
      <g transform="translate(-200, 0)">
        <circle cx="0" cy="0" r="4" fill="#3498db"/>
        <text x="15" y="5" font-family="Microsoft YaHei, sans-serif" font-size="22" fill="#2c3e50">
          小组推选一位代表。
        </text>
      </g>
      
      <g transform="translate(-200, 30)">
        <circle cx="0" cy="0" r="4" fill="#f39c12"/>
        <text x="15" y="5" font-family="Microsoft YaHei, sans-serif" font-size="22" fill="#2c3e50">
          在白板上画出框架草图。
        </text>
      </g>
      
      <g transform="translate(100, 15)">
        <circle cx="0" cy="0" r="4" fill="#27ae60"/>
        <text x="15" y="5" font-family="Microsoft YaHei, sans-serif" font-size="22" fill="#2c3e50">
          接受全场"投资人"的检验。
        </text>
      </g>
    </g>
  </g>
  
  <!-- 装饰性投资人席位 -->
  <g opacity="0.2">
    <g transform="translate(300, 800)">
      <circle cx="0" cy="0" r="8" fill="#34495e"/>
      <rect x="-6" y="8" width="12" height="20" fill="#34495e"/>
    </g>
    
    <g transform="translate(400, 820)">
      <circle cx="0" cy="0" r="8" fill="#34495e"/>
      <rect x="-6" y="8" width="12" height="20" fill="#34495e"/>
    </g>
    
    <g transform="translate(500, 810)">
      <circle cx="0" cy="0" r="8" fill="#34495e"/>
      <rect x="-6" y="8" width="12" height="20" fill="#34495e"/>
    </g>
    
    <g transform="translate(1420, 800)">
      <circle cx="0" cy="0" r="8" fill="#34495e"/>
      <rect x="-6" y="8" width="12" height="20" fill="#34495e"/>
    </g>
    
    <g transform="translate(1520, 820)">
      <circle cx="0" cy="0" r="8" fill="#34495e"/>
      <rect x="-6" y="8" width="12" height="20" fill="#34495e"/>
    </g>
    
    <g transform="translate(1620, 810)">
      <circle cx="0" cy="0" r="8" fill="#34495e"/>
      <rect x="-6" y="8" width="12" height="20" fill="#34495e"/>
    </g>
  </g>
  
  <!-- 装饰性舞台灯光 -->
  <g opacity="0.3">
    <!-- 左侧灯光 -->
    <g transform="translate(200, 200)">
      <circle cx="0" cy="0" r="15" fill="#f1c40f"/>
      <path d="M 0 15 L -40 150 L 40 150 Z" fill="#f39c12" opacity="0.4"/>
    </g>
    
    <!-- 右侧灯光 -->
    <g transform="translate(1720, 200)">
      <circle cx="0" cy="0" r="15" fill="#e74c3c"/>
      <path d="M 0 15 L -40 150 L 40 150 Z" fill="#c0392b" opacity="0.4"/>
    </g>
  </g>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#7f8c8d"/>
    </marker>
  </defs>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.3">
    <path d="M 100 950 Q 960 900 1820 950" stroke="#f39c12" stroke-width="4" fill="none"/>
  </g>
</svg>
