<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <g transform="translate(960, 120)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
      第一板斧：撕开伤口
    </text>
  </g>
  
  <!-- 主视觉：裂开的土地 -->
  <g transform="translate(960, 350)">
    <!-- 裂缝背景 -->
    <g opacity="0.3">
      <rect x="-300" y="-100" width="600" height="200" fill="#8e44ad"/>
      <rect x="-280" y="-80" width="560" height="160" fill="#9b59b6"/>
    </g>
    
    <!-- 裂缝效果 -->
    <g>
      <!-- 主裂缝 -->
      <path d="M -250 -50 Q -100 -30 0 0 Q 100 30 250 50" stroke="#e74c3c" stroke-width="8" fill="none"/>
      <path d="M -250 -50 Q -100 -30 0 0 Q 100 30 250 50" stroke="#c0392b" stroke-width="12" fill="none" opacity="0.5"/>
      
      <!-- 分支裂缝 -->
      <path d="M -100 -30 Q -80 -60 -60 -80" stroke="#e74c3c" stroke-width="4" fill="none"/>
      <path d="M 0 0 Q 20 -40 40 -60" stroke="#e74c3c" stroke-width="4" fill="none"/>
      <path d="M 100 30 Q 120 60 140 80" stroke="#e74c3c" stroke-width="4" fill="none"/>
      
      <!-- 裂缝深度阴影 -->
      <path d="M -250 -45 Q -100 -25 0 5 Q 100 35 250 55" stroke="#2c3e50" stroke-width="3" fill="none" opacity="0.6"/>
    </g>
    
    <!-- 伤口边缘效果 -->
    <g opacity="0.4">
      <circle cx="-200" cy="-40" r="8" fill="#e74c3c"/>
      <circle cx="-50" cy="-15" r="6" fill="#c0392b"/>
      <circle cx="50" cy="15" r="7" fill="#e74c3c"/>
      <circle cx="200" cy="40" r="5" fill="#c0392b"/>
    </g>
  </g>
  
  <!-- 核心要点 -->
  <g transform="translate(960, 550)">
    <!-- 目标 -->
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="32" font-weight="bold" fill="#e74c3c">
      目标：精准说出他的痛苦，引发巨大共鸣。
    </text>
    
    <!-- 技巧框 -->
    <g transform="translate(0, 80)">
      <rect x="-300" y="-40" width="600" height="120" fill="#f8f9fa" rx="15"/>
      <rect x="-280" y="-20" width="560" height="80" fill="#e9ecef" opacity="0.5" rx="10"/>
      
      <text x="0" y="-30" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" font-weight="bold" fill="#2c3e50">
        技巧：
      </text>
      
      <g transform="translate(-150, 0)">
        <circle cx="0" cy="0" r="4" fill="#3498db"/>
        <text x="15" y="5" font-family="Microsoft YaHei, sans-serif" font-size="22" fill="#2c3e50">
          场景化提问
        </text>
      </g>
      
      <g transform="translate(0, 0)">
        <circle cx="0" cy="0" r="4" fill="#f39c12"/>
        <text x="15" y="5" font-family="Microsoft YaHei, sans-serif" font-size="22" fill="#2c3e50">
          扎心数据
        </text>
      </g>
      
      <g transform="translate(150, 0)">
        <circle cx="0" cy="0" r="4" fill="#27ae60"/>
        <text x="15" y="5" font-family="Microsoft YaHei, sans-serif" font-size="22" fill="#2c3e50">
          共情故事
        </text>
      </g>
    </g>
  </g>
  
  <!-- 感受标签 -->
  <g transform="translate(960, 750)">
    <rect x="-150" y="-30" width="300" height="60" fill="#e74c3c" rx="30"/>
    <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" font-weight="bold" fill="#ffffff">
      让他感觉：你比我还懂我！
    </text>
  </g>
  
  <!-- 装饰性伤疤元素 -->
  <g opacity="0.2">
    <!-- 左侧伤疤 -->
    <g transform="translate(200, 600)">
      <path d="M 0 0 Q 20 -10 40 0 Q 60 10 80 0" stroke="#e74c3c" stroke-width="3" fill="none"/>
      <path d="M 10 -5 L 15 5 M 30 -8 L 35 2 M 50 -3 L 55 7 M 70 -6 L 75 4" stroke="#c0392b" stroke-width="2"/>
    </g>
    
    <!-- 右侧伤疤 -->
    <g transform="translate(1640, 700)">
      <path d="M 0 0 Q 15 -8 30 0 Q 45 8 60 0" stroke="#e74c3c" stroke-width="3" fill="none"/>
      <path d="M 8 -4 L 12 4 M 22 -6 L 26 2 M 38 -2 L 42 6 M 52 -5 L 56 3" stroke="#c0392b" stroke-width="2"/>
    </g>
  </g>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.3">
    <path d="M 100 950 Q 960 900 1820 950" stroke="#e74c3c" stroke-width="4" fill="none"/>
  </g>
</svg>
