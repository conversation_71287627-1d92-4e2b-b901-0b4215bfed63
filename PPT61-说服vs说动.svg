<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <g transform="translate(960, 150)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="56" font-weight="bold" fill="#2c3e50">
      说服 vs 说动
    </text>
  </g>
  
  <!-- 左侧：说服 -->
  <g transform="translate(480, 540)">
    <!-- 背景区域 -->
    <rect x="-350" y="-300" width="700" height="600" fill="#ebf3fd" rx="30"/>
    <rect x="-330" y="-280" width="660" height="560" fill="#c8e6c9" opacity="0.3" rx="25"/>
    
    <!-- 大脑图标 -->
    <g transform="translate(0, -150)">
      <ellipse cx="0" cy="0" rx="80" ry="60" fill="#3498db"/>
      <ellipse cx="0" cy="0" rx="70" ry="50" fill="#2980b9"/>
      
      <!-- 大脑纹理 -->
      <g opacity="0.6">
        <path d="M -50 -20 Q -30 -40 0 -20 Q 30 -40 50 -20" stroke="#1f4e79" stroke-width="3" fill="none"/>
        <path d="M -40 0 Q -20 -20 0 0 Q 20 -20 40 0" stroke="#1f4e79" stroke-width="3" fill="none"/>
        <path d="M -30 20 Q -10 0 0 20 Q 10 0 30 20" stroke="#1f4e79" stroke-width="3" fill="none"/>
      </g>
      
      <!-- 理性符号 -->
      <g transform="translate(0, 0)">
        <circle cx="0" cy="0" r="20" fill="#ffffff" opacity="0.8"/>
        <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="16" font-weight="bold" fill="#3498db">
          理
        </text>
      </g>
    </g>
    
    <!-- 标题 -->
    <text x="0" y="-50" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="48" font-weight="bold" fill="#3498db">
      说服 (Persuade)
    </text>
    
    <!-- 武器 -->
    <g transform="translate(0, 20)">
      <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" font-weight="bold" fill="#2c3e50">
        武器：逻辑、证据、道理
      </text>
    </g>
    
    <!-- 攻击目标 -->
    <g transform="translate(0, 70)">
      <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" fill="#2c3e50">
        攻击目标：大脑（理性）
      </text>
    </g>
    
    <!-- 效果 -->
    <g transform="translate(0, 150)">
      <rect x="-80" y="-25" width="160" height="50" fill="#3498db" rx="25"/>
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" font-weight="bold" fill="#ffffff">
        让他"信"
      </text>
    </g>
  </g>
  
  <!-- 右侧：说动 -->
  <g transform="translate(1440, 540)">
    <!-- 背景区域 -->
    <rect x="-350" y="-300" width="700" height="600" fill="#ffebee" rx="30"/>
    <rect x="-330" y="-280" width="660" height="560" fill="#ffcdd2" opacity="0.5" rx="25"/>
    
    <!-- 心脏图标 -->
    <g transform="translate(0, -150)">
      <path d="M 0 20 Q -40 -30 -80 0 Q -80 40 0 100 Q 80 40 80 0 Q 40 -30 0 20 Z" fill="#e74c3c"/>
      <path d="M 0 15 Q -35 -25 -70 0 Q -70 35 0 90 Q 70 35 70 0 Q 35 -25 0 15 Z" fill="#c0392b"/>
      
      <!-- 心跳线 -->
      <g opacity="0.8">
        <path d="M -100 0 L -80 0 L -70 -40 L -60 40 L -50 -30 L -40 0 L 40 0 L 50 -30 L 60 40 L 70 -40 L 80 0 L 100 0" 
              stroke="#ffffff" stroke-width="4" fill="none"/>
      </g>
      
      <!-- 感性符号 -->
      <g transform="translate(0, 10)">
        <circle cx="0" cy="0" r="20" fill="#ffffff" opacity="0.8"/>
        <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="16" font-weight="bold" fill="#e74c3c">
          情
        </text>
      </g>
    </g>
    
    <!-- 标题 -->
    <text x="0" y="-50" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="48" font-weight="bold" fill="#e74c3c">
      说动 (Move)
    </text>
    
    <!-- 武器 -->
    <g transform="translate(0, 20)">
      <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" font-weight="bold" fill="#2c3e50">
        武器：情感、共鸣、故事
      </text>
    </g>
    
    <!-- 攻击目标 -->
    <g transform="translate(0, 70)">
      <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="24" fill="#2c3e50">
        攻击目标：内心（感性）
      </text>
    </g>
    
    <!-- 效果 -->
    <g transform="translate(0, 150)">
      <rect x="-80" y="-25" width="160" height="50" fill="#e74c3c" rx="25"/>
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="28" font-weight="bold" fill="#ffffff">
        让他"爱"
      </text>
    </g>
  </g>
  
  <!-- 中间VS符号 -->
  <g transform="translate(960, 540)">
    <circle cx="0" cy="0" r="60" fill="#f39c12"/>
    <circle cx="0" cy="0" r="45" fill="#e67e22"/>
    <text x="0" y="10" text-anchor="middle" font-family="Microsoft YaHei, sans-serif" font-size="36" font-weight="bold" fill="#ffffff">
      VS
    </text>
  </g>
  
  <!-- 装饰性弧线 -->
  <g opacity="0.3">
    <path d="M 100 950 Q 960 900 1820 950" stroke="#f39c12" stroke-width="4" fill="none"/>
  </g>
</svg>
